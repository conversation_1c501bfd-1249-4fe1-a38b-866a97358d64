<?php
// Importer la classe Auth
use Illuminate\Support\Facades\Auth;

// Connexion à la base de données pour les messages de contact
try {
    $bdd = new PDO('mysql:host=localhost;dbname=task', 'root', '');
    $bdd->query("SET NAMES UTF8");

    // Compter les messages de contact non lus
    $req = $bdd->query("SELECT count(*) as contactmessage FROM contacts where lire = 0");
    $data = $req->fetch();
    $contactCount = $data['contactmessage'];

} catch(Exception $e) {
    $contactCount = 0;
    // Gérer l'erreur silencieusement
}

// Compter les notifications non lues
$notificationCount = 0;
if (Auth::check()) {
    try {
        $notificationCount = \App\Models\Notification::where('user_id', Auth::id())
            ->where('read', false)
            ->count();
    } catch (\Exception $e) {
        // En cas d'erreur, ne pas compter les notifications
        $notificationCount = 0;
    }
}

// Afficher le total des notifications non lues
echo $contactCount + $notificationCount;
?>
<?php /**PATH C:\laragon\www\task\resources\views/userNotif.blade.php ENDPATH**/ ?>