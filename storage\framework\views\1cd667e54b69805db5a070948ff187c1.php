<?php $__currentLoopData = $notifications; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
    <div class="text-reset notification-item d-block dropdown-item position-relative <?php echo e($notification->read ? '' : 'bg-light-subtle'); ?>">
        <div class="d-flex">
            <div class="avatar-xs me-3 flex-shrink-0">
                <?php if($notification->type == 'tache_created'): ?>
                    <span class="avatar-title bg-success-subtle text-success rounded-circle fs-16">
                        <i class="ri-task-line"></i>
                    </span>
                <?php elseif($notification->type == 'tache_updated'): ?>
                    <span class="avatar-title bg-info-subtle text-info rounded-circle fs-16">
                        <i class="ri-edit-box-line"></i>
                    </span>
                <?php elseif($notification->type == 'tache_assigned'): ?>
                    <span class="avatar-title bg-primary-subtle text-primary rounded-circle fs-16">
                        <i class="ri-user-follow-line"></i>
                    </span>
                <?php else: ?>
                    <span class="avatar-title bg-secondary-subtle text-secondary rounded-circle fs-16">
                        <i class="ri-notification-line"></i>
                    </span>
                <?php endif; ?>
            </div>
            <div class="flex-grow-1">
                <h6 class="mt-0 mb-1 fs-13 fw-semibold"><?php echo e($notification->message); ?></h6>
                <?php if($notification->tache_id): ?>
                    <div class="mt-1">
                        <a href="<?php echo e(route('tache-detail', $notification->tache_id)); ?>" class="btn btn-sm btn-soft-primary">
                            <i class="ri-eye-line"></i> <?php echo e(__('message.View Task')); ?>

                        </a>
                        <button type="button" class="btn btn-sm btn-soft-success mark-as-read-btn" data-id="<?php echo e($notification->id); ?>">
                            <i class="ri-check-double-line"></i> <?php echo e(__('message.Mark as read')); ?>

                        </button>
                    </div>
                <?php else: ?>
                    <div class="mt-1">
                        <button type="button" class="btn btn-sm btn-soft-success mark-as-read-btn" data-id="<?php echo e($notification->id); ?>">
                            <i class="ri-check-double-line"></i> <?php echo e(__('message.Mark as read')); ?>

                        </button>
                    </div>
                <?php endif; ?>
                <p class="mb-0 fs-11 fw-medium text-uppercase text-muted">
                    <span><i class="mdi mdi-clock-outline"></i> <?php echo e($notification->created_at->diffForHumans()); ?></span>
                </p>
            </div>
            <div class="px-2 fs-15">
                <div class="form-check notification-check">
                    <input class="form-check-input" type="checkbox" value="" id="notification-check-<?php echo e($notification->id); ?>" <?php echo e($notification->read ? 'checked' : ''); ?>>
                    <label class="form-check-label" for="notification-check-<?php echo e($notification->id); ?>"></label>
                </div>
            </div>
        </div>
    </div>
<?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

<?php if(count($notifications) == 0): ?>
    <div class="text-center p-3">
        <h6 class="mb-0 fs-16 fw-semibold"><?php echo e(__('message.No notifications')); ?></h6>
    </div>
<?php else: ?>
    <div class="my-3 text-center view-all">
        <a href="<?php echo e(route('notifications.all')); ?>" class="btn btn-soft-success waves-effect waves-light">
            <?php echo e(__('message.View all notifications')); ?> <i class="ri-arrow-right-line align-middle"></i>
        </a>
    </div>
<?php endif; ?>
<?php /**PATH C:\laragon\www\task\resources\views/partials/notifications.blade.php ENDPATH**/ ?>