<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{

    public function up(): void
{
    Schema::create('users', function (Blueprint $table) {
        $table->id();
        $table->enum('role', [ 'admin', 'entreprise', 'client','membre' ])->default('admin');
        $table->string('name');
        $table->string('poste')->nullable(); // 👈 Ajout du champ poste (nullable si pas obligatoire)
        $table->string('email')->unique();
        $table->timestamp('email_verified_at')->nullable();
        $table->string('password');
        $table->tinyInteger('paiement')->default(0);
        $table->rememberToken();
        $table->timestamps();
        
    });
}

    

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('users');
    }
};
