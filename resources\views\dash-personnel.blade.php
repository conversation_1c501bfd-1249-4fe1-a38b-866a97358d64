@extends($userTheme ?? 'persotheme')

@section('contenu')
<!-- Inclure Animate.css pour les animations -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
                    <h4 class="mb-sm-0">
                        <i class="ri-dashboard-line text-primary me-2"></i> {{ __('message.Staff Dashboard') }}
                    </h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">{{ __('message.TaskFlow') }}</a></li>
                            <li class="breadcrumb-item active">{{ __('message.Staff Dashboard') }}</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <!-- Statistiques personnelles -->
        <div class="row g-3 mb-4">
            <div class="col-md-6">
                <div class="card card-animate h-100 shadow-sm border-0 animate__animated animate__fadeIn">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm flex-shrink-0 me-3">
                                <div class="avatar-title bg-primary-subtle rounded-circle fs-1">
                                    <i data-feather="user" class="text-primary"></i>
                                </div>
                            </div>
                            <div>
                                <h5 class="card-title mb-1">{{ __('message.Welcome') }}, {{ Auth::user()->name }}!</h5>
                                <p class="text-muted mb-0">{{ Auth::user()->poste ?? __('message.Staff Member') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card card-animate h-100 shadow-sm border-0 animate__animated animate__fadeIn animate__delay-1s">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm flex-shrink-0 me-3">
                                <div class="avatar-title bg-success-subtle rounded-circle fs-1">
                                    <i data-feather="check-circle" class="text-success"></i>
                                </div>
                            </div>
                            <div>
                                <h5 class="card-title mb-1">{{ __('message.Your Tasks') }}</h5>
                                <p class="text-muted mb-0">{{ __('message.Manage your assigned tasks') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tâches assignées -->
        <div class="card shadow-lg border-0 animate__animated animate__fadeIn animate__delay-2s">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="ri-task-line text-primary me-2"></i> {{ __('message.Your Assigned Tasks') }}
                </h5>
                <div>
                    <a href="{{ route('taches-list') }}" class="btn btn-sm btn-primary">
                        <i class="ri-list-check me-1"></i> {{ __('message.View All Tasks') }}
                    </a>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover table-striped align-middle">
                        <thead class="table-light">
                            <tr>
                                <th>{{ __('message.Task Name') }}</th>
                                <th>{{ __('message.Project') }}</th>
                                <th>{{ __('message.Deadline') }}</th>
                                <th>{{ __('message.Status') }}</th>
                                <th>{{ __('message.Progress') }}</th>
                                <th>{{ __('message.Actions') }}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Ici, vous devrez ajouter une logique pour récupérer les tâches assignées au membre connecté -->
                            @if(isset($taches) && count($taches) > 0)
                                @foreach($taches as $tache)
                                <tr>
                                    <td>{{ $tache->nom }}</td>
                                    <td>{{ $tache->projet->project_name }}</td>
                                    <td>{{ \Carbon\Carbon::parse($tache->deadline)->format('d/m/Y') }}</td>
                                    <td>
                                        @if($tache->statut == 'to_do')
                                            <span class="badge bg-secondary">{{ __('message.To Do') }}</span>
                                        @elseif($tache->statut == 'doing')
                                            <span class="badge bg-warning">{{ __('message.In Progress') }}</span>
                                        @elseif($tache->statut == 'bug')
                                            <span class="badge bg-danger">{{ __('message.Bug') }}</span>
                                        @elseif($tache->statut == 'done')
                                            <span class="badge bg-success">{{ __('message.Done') }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <div class="progress" style="height: 8px;">
                                            <div class="progress-bar
                                                @if($tache->statut == 'to_do') bg-secondary
                                                @elseif($tache->statut == 'doing') bg-warning
                                                @elseif($tache->statut == 'bug') bg-danger
                                                @elseif($tache->statut == 'done') bg-success
                                                @endif
                                                progress-bar-striped progress-bar-animated"
                                                role="progressbar" style="width: {{ $tache->avancement }}%;"
                                                aria-valuenow="{{ $tache->avancement }}" aria-valuemin="0" aria-valuemax="100">
                                            </div>
                                        </div>
                                        <small class="text-muted">{{ $tache->avancement }}%</small>
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ route('tache-detail', ['id' => $tache->id]) }}" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="{{ __('message.View Details') }}">
                                                <i class="ri-eye-fill"></i>
                                            </a>
                                            <a href="/modifier-tache/{{ $tache->id }}" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="{{ __('message.Edit') }}">
                                                <i class="ri-edit-2-line"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                @endforeach
                            @else
                                <tr>
                                    <td colspan="6" class="text-center">
                                        <div class="alert alert-info mb-0">
                                            <i class="ri-information-line me-2"></i> {{ __('message.No tasks assigned yet') }}
                                        </div>
                                    </td>
                                </tr>
                            @endif
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

    </div>
</div>

<script>
    // Initialiser les tooltips Bootstrap
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
@endsection
