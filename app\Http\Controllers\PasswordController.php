<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class PasswordController extends Controller
{
    /**
     * Affiche le formulaire pour définir un nouveau mot de passe
     */
    public function showSetPasswordForm($user_id)
    {
        // Vérifier si l'utilisateur existe
        $user = User::find($user_id);

        if (!$user) {
            return redirect('/login')->with('error', 'Utilisateur non trouvé.');
        }

        return view('auth.set-password', compact('user_id'));
    }

    /**
     * Traite la demande de changement de mot de passe
     */
    public function setPassword(Request $request)
    {
        // Validation des données
        $validator = Validator::make($request->all(), [
            'user_id' => 'required|exists:users,id',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Récupérer l'utilisateur
        $user = User::findOrFail($request->user_id);

        // Mettre à jour le mot de passe
        $user->password = Hash::make($request->password);
        $user->save();

        // Connecter l'utilisateur
        Auth::login($user);

        // Rediriger vers la page de paiement
        return redirect('/checkout')->with('success', 'Votre mot de passe a été défini avec succès. Veuillez procéder au paiement pour activer votre compte.');
    }
}
