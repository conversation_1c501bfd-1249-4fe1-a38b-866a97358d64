<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8" />
    <title>Définir votre mot de passe | TaskFlow</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="TaskFlow - Gestion de projets et de tâches" name="description" />
    <meta content="TaskFlow" name="author" />

    <!-- App favicon -->
    <link rel="shortcut icon" href="{{ asset('assets/images/favicon.ico') }}">

    <!-- Bootstrap Css -->
    <link href="{{ asset('assets/css/bootstrap.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- Icons Css -->
    <link href="{{ asset('assets/css/icons.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- App Css-->
    <link href="{{ asset('assets/css/app.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- Custom Css-->
    <link href="{{ asset('assets/css/custom.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />

    <style>
        body {
            background-color: #f5f7fb;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            font-family: 'Poppins', sans-serif;
        }

        .auth-card {
            max-width: 500px;
            width: 100%;
            background-color: #fff;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(31, 45, 61, 0.07);
            overflow: hidden;
            animation: fadeIn 0.5s ease;
            border: 1px solid #e5e9f2;
        }

        .auth-header {
            background-color: #6576ff;
            padding: 20px;
            text-align: center;
            color: white;
            border-bottom: 1px solid #e5e9f2;
        }

        .auth-body {
            padding: 25px;
        }

        .auth-logo {
            margin-bottom: 20px;
        }

        .auth-logo img {
            max-width: 150px;
            height: auto;
        }

        .password-criteria {
            background-color: #f5f7fb;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #e5e9f2;
        }

        .btn-primary {
            background-color: #6576ff;
            border: none;
            transition: all 0.3s ease;
            font-weight: 600;
            letter-spacing: 0.02em;
            border-radius: 4px;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(101, 118, 255, 0.3);
            background-color: #5664e0;
        }

        .progress {
            height: 6px;
            border-radius: 3px;
            background-color: #e5e9f2;
        }

        .form-control {
            border: 1px solid #e5e9f2;
            border-radius: 4px;
            padding: 10px 12px;
        }

        .form-control:focus {
            box-shadow: 0 0 0 0.25rem rgba(101, 118, 255, 0.25);
            border-color: #6576ff;
        }

        .auth-footer {
            text-align: center;
            padding: 15px;
            background-color: #f5f7fb;
            border-top: 1px solid #e5e9f2;
            color: #526484;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .criteria-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            color: #526484;
        }

        .criteria-item i {
            margin-right: 8px;
            transition: color 0.3s ease;
        }

        .eye-toggle {
            background: none;
            border: none;
            color: #526484;
            position: absolute;
            right: 10px;
            top: 50%;
            transform: translateY(-50%);
            z-index: 10;
            cursor: pointer;
        }

        .password-input-group {
            position: relative;
        }

        h5 {
            color: #364a63;
            font-weight: 600;
        }

        .text-primary {
            color: #6576ff !important;
        }

        .text-muted {
            color: #526484 !important;
        }

        .form-label {
            color: #364a63;
            font-weight: 500;
        }
    </style>
</head>

<body>
    <div class="auth-card">
        <div class="auth-header">
            <h4 class="mb-0"><i class="ri-lock-2-line me-2"></i> Définir votre mot de passe</h4>
        </div>

        <div class="auth-body">
            <div class="text-center auth-logo">
                <img src="{{ asset('assets/images/newnewnew.png') }}" alt="TaskFlow Logo">
                <h5 class="mt-3 text-primary">Bienvenue sur TaskFlow</h5>
                <p class="text-muted">Créez un mot de passe sécurisé pour accéder à votre compte</p>
            </div>

            @if(session('error'))
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="ri-error-warning-line me-2"></i> {{ session('error') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            @if(session('success'))
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="ri-checkbox-circle-line me-2"></i> {{ session('success') }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            @endif

            <form action="{{ route('client.set-password') }}" method="POST" class="needs-validation" novalidate>
                @csrf
                <input type="hidden" name="user_id" value="{{ $user_id }}">

                <!-- Champ de mot de passe avec indicateur de force -->
                <div class="mb-3">
                    <label for="password" class="form-label fw-medium">Nouveau mot de passe</label>
                    <div class="password-input-group">
                        <input type="password" class="form-control @error('password') is-invalid @enderror"
                               placeholder="Entrez votre mot de passe" id="password" name="password" required>
                        <button type="button" class="eye-toggle" id="password-toggle">
                            <i class="ri-eye-line"></i>
                        </button>
                        @error('password')
                            <span class="invalid-feedback" role="alert">
                                <strong>{{ $message }}</strong>
                            </span>
                        @enderror
                    </div>

                    <!-- Indicateur de force du mot de passe -->
                    <div class="mt-2">
                        <div class="progress">
                            <div class="progress-bar bg-danger" role="progressbar" id="password-strength-bar" style="width: 0%"></div>
                        </div>
                        <div class="d-flex justify-content-between mt-1">
                            <small class="text-muted" id="password-strength-text">Force du mot de passe</small>
                            <small class="text-muted"><i class="ri-information-line"></i> Min. 8 caractères</small>
                        </div>
                    </div>
                </div>

                <!-- Champ de confirmation du mot de passe -->
                <div class="mb-4">
                    <label for="password_confirmation" class="form-label fw-medium">Confirmer le mot de passe</label>
                    <div class="password-input-group">
                        <input type="password" class="form-control"
                               placeholder="Confirmez votre mot de passe" id="password_confirmation"
                               name="password_confirmation" required>
                        <button type="button" class="eye-toggle" id="confirm-password-toggle">
                            <i class="ri-eye-line"></i>
                        </button>
                    </div>
                    <div id="password-match" class="form-text mt-1"></div>
                </div>

                <!-- Critères de sécurité du mot de passe -->
                <div class="password-criteria mb-4">
                    <h6 class="mb-3 fw-medium"><i class="ri-shield-keyhole-line text-primary me-1"></i> Critères de sécurité</h6>
                    <div class="row">
                        <div class="col-6">
                            <div class="criteria-item">
                                <i class="ri-checkbox-circle-line" id="length-check" style="color: #ccc;"></i>
                                <small>8 caractères minimum</small>
                            </div>
                            <div class="criteria-item">
                                <i class="ri-checkbox-circle-line" id="uppercase-check" style="color: #ccc;"></i>
                                <small>Une majuscule</small>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="criteria-item">
                                <i class="ri-checkbox-circle-line" id="number-check" style="color: #ccc;"></i>
                                <small>Un chiffre</small>
                            </div>
                            <div class="criteria-item">
                                <i class="ri-checkbox-circle-line" id="special-check" style="color: #ccc;"></i>
                                <small>Un caractère spécial</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bouton de soumission -->
                <div class="mb-4">
                    <button class="btn btn-primary w-100 py-2" type="submit">
                        <i class="ri-lock-unlock-line me-1"></i> Continuer vers le paiement
                    </button>
                </div>
            </form>
        </div>

        <div class="auth-footer">
            <p class="mb-0">
                <i class="ri-information-line me-1"></i>
                Après avoir défini votre mot de passe, vous devrez effectuer un paiement de 100 dinars pour activer votre compte.
            </p>
        </div>
    </div>

    <!-- JAVASCRIPT -->
    <script src="{{ asset('assets/libs/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('assets/libs/simplebar/simplebar.min.js') }}"></script>
    <script src="{{ asset('assets/libs/node-waves/waves.min.js') }}"></script>
    <script src="{{ asset('assets/libs/feather-icons/feather.min.js') }}"></script>
    <script src="{{ asset('assets/js/pages/plugins/lord-icon-2.1.0.js') }}"></script>
    <script src="{{ asset('assets/js/plugins.js') }}"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Éléments du DOM
            const passwordInput = document.getElementById('password');
            const confirmPasswordInput = document.getElementById('password_confirmation');
            const strengthBar = document.getElementById('password-strength-bar');
            const strengthText = document.getElementById('password-strength-text');
            const passwordMatch = document.getElementById('password-match');

            // Éléments pour les critères de sécurité
            const lengthCheck = document.getElementById('length-check');
            const uppercaseCheck = document.getElementById('uppercase-check');
            const numberCheck = document.getElementById('number-check');
            const specialCheck = document.getElementById('special-check');

            // Toggle password visibility
            document.getElementById('password-toggle').addEventListener('click', function() {
                togglePasswordVisibility(passwordInput, this);
            });

            document.getElementById('confirm-password-toggle').addEventListener('click', function() {
                togglePasswordVisibility(confirmPasswordInput, this);
            });

            function togglePasswordVisibility(inputElement, buttonElement) {
                const type = inputElement.getAttribute('type') === 'password' ? 'text' : 'password';
                inputElement.setAttribute('type', type);

                // Change icon
                const icon = buttonElement.querySelector('i');
                if (type === 'text') {
                    icon.classList.remove('ri-eye-line');
                    icon.classList.add('ri-eye-off-line');
                } else {
                    icon.classList.remove('ri-eye-off-line');
                    icon.classList.add('ri-eye-line');
                }
            }

            // Fonction pour vérifier la force du mot de passe et mettre à jour l'interface
            function checkPasswordStrength(password) {
                let strength = 0;
                let feedback = '';

                // Vérifier les critères
                const hasLength = password.length >= 8;
                const hasUppercase = /[A-Z]/.test(password);
                const hasNumber = /[0-9]/.test(password);
                const hasSpecial = /[^A-Za-z0-9]/.test(password);

                // Mettre à jour les indicateurs visuels des critères
                lengthCheck.style.color = hasLength ? '#28a745' : '#ccc';
                uppercaseCheck.style.color = hasUppercase ? '#28a745' : '#ccc';
                numberCheck.style.color = hasNumber ? '#28a745' : '#ccc';
                specialCheck.style.color = hasSpecial ? '#28a745' : '#ccc';

                // Calculer la force
                if (hasLength) strength += 25;
                if (hasUppercase) strength += 25;
                if (hasNumber) strength += 25;
                if (hasSpecial) strength += 25;

                // Animer la barre de progression
                strengthBar.style.width = strength + '%';

                // Définir la classe et le texte en fonction de la force
                if (password.length === 0) {
                    strengthBar.className = 'progress-bar';
                    feedback = 'Force du mot de passe';
                } else if (strength <= 25) {
                    strengthBar.className = 'progress-bar bg-danger';
                    feedback = 'Très faible';
                } else if (strength <= 50) {
                    strengthBar.className = 'progress-bar bg-warning';
                    feedback = 'Faible';
                } else if (strength <= 75) {
                    strengthBar.className = 'progress-bar bg-info';
                    feedback = 'Moyen';
                } else {
                    strengthBar.className = 'progress-bar bg-success';
                    feedback = 'Fort';
                }

                strengthText.textContent = feedback;
            }

            // Vérifier la correspondance des mots de passe
            function checkPasswordMatch() {
                if (confirmPasswordInput.value === '') {
                    passwordMatch.textContent = '';
                    passwordMatch.className = 'form-text mt-1';
                    return;
                }

                if (confirmPasswordInput.value === passwordInput.value) {
                    passwordMatch.innerHTML = '<i class="ri-checkbox-circle-fill me-1 text-success"></i> Les mots de passe correspondent';
                    passwordMatch.className = 'form-text text-success mt-1';
                } else {
                    passwordMatch.innerHTML = '<i class="ri-error-warning-fill me-1 text-danger"></i> Les mots de passe ne correspondent pas';
                    passwordMatch.className = 'form-text text-danger mt-1';
                }
            }

            // Événements pour la vérification en temps réel
            passwordInput.addEventListener('input', function() {
                checkPasswordStrength(this.value);
                if (confirmPasswordInput.value !== '') {
                    checkPasswordMatch();
                }
            });

            confirmPasswordInput.addEventListener('input', checkPasswordMatch);

            // Initialiser les vérifications
            checkPasswordStrength(passwordInput.value);
            checkPasswordMatch();
        });
    </script>
</body>
</html>
