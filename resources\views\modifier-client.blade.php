@extends('admintheme')
@section('contenu')

<div class="page-content">
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
                    <h4 class="mb-sm-0">
                        <i class="ri-user-3-line text-primary me-2"></i>{{ __('message.Client Details') }}
                    </h4>
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">TaskFlow</a></li>
                            <li class="breadcrumb-item"><a href="{{ route('clients.index') }}">{{ __('message.Clients') }}</a></li>
                            <li class="breadcrumb-item active">{{ __('message.Client Details') }}</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row">
            <div class="col-lg-12">
                <div class="card shadow-lg border-0 animate__animated animate__fadeIn">
                    <div class="card-header d-flex align-items-center bg-light">
                        <h5 class="card-title mb-0 flex-grow-1">{{ __('message.Edit Client Information') }}</h5>
                        <div class="flex-shrink-0">
                            <a href="{{ route('clients.index') }}" class="btn btn-secondary">
                                <i class="ri-arrow-left-line align-bottom me-1"></i> {{ __('message.Back to List') }}
                            </a>
                        </div>
                    </div>
                    <div class="card-body">
                        @if(session('success'))
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                {{ session('success') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        @endif

                        @if(session('error'))
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                {{ session('error') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        @endif

                        <form action="{{ route('clients.update', $client->id) }}" method="POST">
                            @csrf
                            @method('PUT')

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="name" class="form-label">{{ __('message.Name') }}</label>
                                    <input type="text" class="form-control" id="name" name="name" value="{{ $client->name }}" required>
                                    @error('name')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="col-md-6">
                                    <label for="email" class="form-label">{{ __('message.Email') }}</label>
                                    <input type="email" class="form-control" id="email" name="email" value="{{ $client->email }}" required>
                                    @error('email')
                                        <div class="text-danger">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="created_at" class="form-label">{{ __('message.Registration Date') }}</label>
                                    <input type="text" class="form-control" id="created_at" value="{{ $client->created_at->format('d/m/Y H:i') }}" readonly>
                                </div>

                                <div class="col-md-6">
                                    <label for="role" class="form-label">{{ __('message.Role') }}</label>
                                    <input type="text" class="form-control" id="role" value="{{ $client->role }}" readonly>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12 text-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="ri-save-line align-bottom me-1"></i> {{ __('message.Save Changes') }}
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Projects Section -->
        <div class="row mt-4">
            <div class="col-lg-12">
                <div class="card shadow-lg border-0 animate__animated animate__fadeIn">
                    <div class="card-header d-flex align-items-center bg-light">
                        <h5 class="card-title mb-0 flex-grow-1">{{ __('message.Client Projects') }}</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped align-middle mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">{{ __('message.Project Name') }}</th>
                                        <th scope="col">{{ __('message.Start Date') }}</th>
                                        <th scope="col">{{ __('message.End Date') }}</th>
                                        <th scope="col">{{ __('message.Status') }}</th>
                                        <th scope="col">{{ __('message.Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($client->projets as $projet)
                                        <tr>
                                            <td>{{ $projet->project_name }}</td>
                                            <td>{{ $projet->start_date }}</td>
                                            <td>{{ $projet->end_date }}</td>
                                            <td>
                                                @php
                                                    $now = \Carbon\Carbon::now();
                                                    $startDate = \Carbon\Carbon::parse($projet->start_date);
                                                    $endDate = \Carbon\Carbon::parse($projet->end_date);

                                                    if ($now < $startDate) {
                                                        $status = __('message.Not Started');
                                                        $badgeClass = 'bg-warning';
                                                    } elseif ($now > $endDate) {
                                                        $status = __('message.Completed');
                                                        $badgeClass = 'bg-success';
                                                    } else {
                                                        $status = __('message.In Progress');
                                                        $badgeClass = 'bg-info';
                                                    }
                                                @endphp
                                                <span class="badge {{ $badgeClass }}">{{ $status }}</span>
                                            </td>
                                            <td>
                                                <a href="{{ route('projet-detail', ['id' => $projet->id]) }}" class="btn btn-sm btn-info">
                                                    <i class="ri-eye-line"></i> {{ __('message.View') }}
                                                </a>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="5" class="text-center">{{ __('message.No projects found for this client') }}</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection
