
<?php $__env->startSection('contenu'); ?>
<div class="container-fluid">

    <!-- Début titre de la page -->
    <!-- Fin titre de la page -->

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">Modifier un administrateur</h4>
                    <div class="flex-shrink-0">
                    </div>
                </div><!-- fin entête de carte -->

                <div class="card-body">
                    <div class="live-preview">
                        <div class="row gy-4">
                            <form action="/modifAdmin" method="POST">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="id" value="<?php echo e($data->id); ?>">

                                <div class="col-xxl-12 col-md-12">
                                    <div>
                                        <label for="basiInput" class="form-label">Nom</label>
                                        <input type="text" placeholder="Nom" class="form-control" name="name" value="<?php echo e($data->name); ?>" id="basiInput">
                                    </div>
                                </div>

                                <div class="col-xxl-12 col-md-12">
                                    <div>
                                        <label for="labelInput" class="form-label">E-mail</label>
                                        <input type="email" placeholder="E-mail" class="form-control" name="email" value="<?php echo e($data->email); ?>" id="labelInput">
                                    </div>
                                </div>

                                <div class="col-xxl-12 col-md-12">
                                    <div>
                                        <label for="placeholderInput" class="form-label">Mot de passe</label>
                                        <input type="password" placeholder="Mot de passe" class="form-control" name="password" value="<?php echo e($data->password); ?>" id="placeholderInput">
                                    </div>
                                </div>

                                <div class="col-xxl-12 col-md-12">
                                    <button type="submit" class="btn btn-primary">Modifier</button>
                                </div>
                            </form>
                        </div>

                        <div class="d-none code-view">
                            <pre class="language-markup" style="height: 450px;"><code>&lt;!-- Formulaire de modification admin --&gt;
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- fin col -->
    </div>
    <!-- fin ligne -->

</div> <!-- container-fluid -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make($userTheme ?? 'theme', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\task\resources\views/modifier-admin.blade.php ENDPATH**/ ?>