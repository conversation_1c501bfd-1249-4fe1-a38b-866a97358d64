<?php
namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class LangueController extends Controller
{
    public function change($lang)
    {
        // Vérifier si la langue est supportée
        if (in_array($lang, ['en', 'fr'])) {
            // Stocker la langue dans la session
            session(['locale' => $lang]);

            // Définir la locale pour la requête en cours
            App::setLocale($lang);
        }

        // Rediriger vers la page précédente
        return redirect()->back();
    }
}