<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;
use Symfony\Component\HttpFoundation\Response;

class ThemeSelector
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Vérifier si l'utilisateur est authentifié
        if (Auth::check()) {
            // Utiliser la classe ThemeHelper pour déterminer le thème
            $theme = \App\Helpers\ThemeHelper::getUserTheme();

            // Partager la variable de thème avec toutes les vues
            View::share('userTheme', $theme);
        }

        return $next($request);
    }
}
