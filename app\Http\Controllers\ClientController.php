<?php

namespace App\Http\Controllers;

use App\Models\User;
use App\Models\ClientInvitation;
use Illuminate\Http\Request;

class ClientController extends Controller
{
    /**
     * Affiche la liste des clients liés à l'entreprise connectée
     */
    public function index()
    {
        // Si l'utilisateur est une entreprise, montrer seulement les clients qu'il a ajoutés
        if (auth()->user()->role == 'entreprise') {
            $clients = User::where('role', 'client')
                      ->where('entreprise_id', auth()->id())
                      ->get();
        } else {
            // Pour les admins, montrer tous les clients
            $clients = User::where('role', 'client')->get();
        }

        return view('liste-client', compact('clients'));
    }

    /**
     * Affiche les détails d'un client spécifique
     */
    public function show($id)
    {
        $client = User::findOrFail($id);
        
        // Vérifier que l'utilisateur a le droit de voir ce client
        if (auth()->user()->role == 'entreprise' && $client->entreprise_id != auth()->id()) {
            return redirect()->back()->with('error', 'Vous n\'êtes pas autorisé à voir ce client.');
        }
        
        return view('modifier-client', compact('client'));
    }

    /**
     * Met à jour les informations d'un client
     */
    public function update(Request $request, $id)
    {
        $client = User::findOrFail($id);
        
        // Vérifier que l'utilisateur a le droit de modifier ce client
        if (auth()->user()->role == 'entreprise' && $client->entreprise_id != auth()->id()) {
            return redirect()->back()->with('error', 'Vous n\'êtes pas autorisé à modifier ce client.');
        }
        
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email,' . $id,
        ]);
        
        $client->update($validated);
        
        return redirect()->route('clients.index')->with('success', 'Client mis à jour avec succès.');
    }

    /**
     * Supprime un client
     */
    public function destroy($id)
    {
        $client = User::findOrFail($id);
        
        // Vérifier que l'utilisateur a le droit de supprimer ce client
        if (auth()->user()->role == 'entreprise' && $client->entreprise_id != auth()->id()) {
            return redirect()->back()->with('error', 'Vous n\'êtes pas autorisé à supprimer ce client.');
        }
        
        $client->delete();
        
        return redirect()->route('clients.index')->with('success', 'Client supprimé avec succès.');
    }
}
