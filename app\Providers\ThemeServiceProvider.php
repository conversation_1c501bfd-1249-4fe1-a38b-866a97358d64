<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\View;

class ThemeServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        // Créer une directive Blade pour étendre dynamiquement le thème approprié
        Blade::directive('extendTheme', function () {
            return '<?php
                $theme = isset($userTheme) ? $userTheme : \App\Helpers\ThemeHelper::getUserTheme();
                echo "@extends(\"$theme\")";
            ?>';
        });

        // Partager la fonction de détection de thème avec toutes les vues
        View::composer('*', function ($view) {
            if (Auth::check()) {
                // Utiliser la classe ThemeHelper pour déterminer le thème
                $theme = \App\Helpers\ThemeHelper::getUserTheme();

                $view->with('userTheme', $theme);
            }
        });
    }
}
