@extends($userTheme ?? 'theme')
@section('contenu')

<div class="container-fluid">

    <!-- début du titre de la page -->
    <!-- fin du titre de la page -->

    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">{{ __('message.Admin List') }}</h4>
                    <div class="flex-shrink-0">
                    </div>
                </div><!-- fin de l'en-tête de la carte -->

                <div class="card-body">

                    <div class="live-preview">
                        <div class="table-responsive">
                            @if(session('message'))
                            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                {{ session('message') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
                            </div>
                            @endif

                            <table class="table align-middle table-nowrap mb-0">
                                <thead>
                                    <tr>
                                        <th scope="col">{{ __('message.ID_admin') }}</th>
                                        <th scope="col">{{ __('message.Name_admin_list') }}</th>
                                        <th scope="col">{{ __('message.Email_admin') }}</th>
                                        <th scope="col">{{ __('message.Password_admin') }}</th>
                                        <th scope="col">{{ __('message.Action_admin') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach ($data as $item)
                                    <tr>
                                        <th scope="row"><a href="#" class="fw-medium">#{{ $item->id }}</a></th>
                                        <td>{{ $item->name }}</td>
                                        <td>{{ $item->email }}</td>
                                        <td>************</td>
                                        <td>
                                            <a href="/modifier-admin/{{ $item->id }}" type="button" class="btn btn-primary">
                                                {{ __('message.Edit_admin') }} <i class="bx bx-edit-alt"></i>
                                            </a>
                                            <a href="/suppAdmin/{{ $item->id }}" class="btn btn-danger" onclick="return confirm('{{ __('message.Confirm delete admin') }}')">
                                                {{ __('message.Delete_admin') }} <i class="bx bx-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div><!-- fin du corps de la carte -->
            </div><!-- fin de la carte -->
        </div>
        <!-- fin col -->
    </div>
    <!-- fin ligne -->

</div>
@endsection
