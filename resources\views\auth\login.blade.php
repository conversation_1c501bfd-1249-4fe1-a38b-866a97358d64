@extends('logintheme')

@section('contenu')
<div class="card-body p-4">
    <div class="text-center mt-2">
        <h5 class="text-primary fw-bold mb-4 animate__animated animate__fadeInDown">{{ __('auth.Welcome Back!') }}</h5>
    </div>

    <div class="p-2 mt-4">
        <form method="POST" action="{{ route('login') }}" class="animate__animated animate__fadeIn animate__delay-1s">
            @csrf

            <div class="mb-4">
                <div class="form-floating">
                    <input id="email" type="email" class="form-control @error('email') is-invalid @enderror" name="email" value="{{ old('email') }}" required autocomplete="email" autofocus placeholder="{{ __('auth.Email Address') }}">
                    <label for="email">{{ __('auth.Email Address') }}</label>
                    @error('email')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                    @enderror
                </div>
            </div>

            <div class="mb-4">
                <div class="form-floating">
                    <input id="password" type="password" class="form-control @error('password') is-invalid @enderror" name="password" required autocomplete="current-password" placeholder="{{ __('auth.Password') }}">
                    <label for="password">{{ __('auth.Password') }}</label>
                    @error('password')
                        <div class="invalid-feedback">
                            {{ $message }}
                        </div>
                    @enderror
                </div>
            </div>

            <div class="form-check mb-4">
                <input class="form-check-input" type="checkbox" name="remember" id="remember" {{ old('remember') ? 'checked' : '' }}>
                <label class="form-check-label" for="remember">
                    {{ __('auth.Remember Me') }}
                </label>
            </div>

            <div class="mb-4">
                <button type="submit" class="btn btn-primary w-100 waves-effect waves-light hover-shadow">
                    <i class="ri-login-box-line align-middle me-1"></i> {{ __('auth.Login') }}
                </button>
            </div>

            <div class="mt-4 text-center">
                @if (Route::has('password.request'))
                    <a class="text-muted" href="{{ route('password.request') }}">
                        <i class="ri-lock-password-line align-middle"></i> {{ __('auth.Forgot Your Password?') }}
                    </a>
                @endif
            </div>
        </form>
    </div>

    <div class="mt-4 text-center">
        <div class="signin-other-title">
            <h5 class="fs-13 mb-4 title">{{ __('auth.Sign In with') }}</h5>
        </div>
        <div>
            <a href="{{ url('auth/google')}}" class="btn btn-soft-info w-100 hover-shadow">
                <i class="ri-google-fill align-middle me-1"></i> {{ __('message.login using google') }}
            </a>
        </div>
    </div>
</div>
@endsection
