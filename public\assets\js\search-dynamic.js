/**
 * Gestion de la recherche dynamique
 */
document.addEventListener('DOMContentLoaded', function() {
    // Sélectionner les éléments du DOM
    const searchInput = document.getElementById('search-options');
    const searchDropdown = document.getElementById('search-dropdown');
    const searchCloseBtn = document.getElementById('search-close-options');
    
    if (!searchInput || !searchDropdown) return;

    // <PERSON><PERSON>lai avant de lancer la recherche (en ms)
    const searchDelay = 300;
    let searchTimeout;
    let lastQuery = '';

    // Fonction pour effectuer la recherche
    function performSearch(query) {
        if (query === lastQuery) return;
        lastQuery = query;

        // Si la requête est vide ou trop courte, vider les résultats
        if (!query || query.length < 2) {
            clearSearchResults();
            return;
        }

        // Afficher l'indicateur de chargement
        showLoadingIndicator();

        // Effectuer la requête AJAX
        fetch(`/api/search?query=${encodeURIComponent(query)}`)
            .then(response => response.json())
            .then(data => {
                // Mettre à jour les résultats
                updateSearchResults(data);
            })
            .catch(error => {
                console.error('Erreur lors de la recherche:', error);
                clearSearchResults();
            });
    }

    // Fonction pour afficher l'indicateur de chargement
    function showLoadingIndicator() {
        // Vider les résultats actuels
        const resultsContainer = searchDropdown.querySelector('[data-simplebar]');
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="p-3 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                    <p class="mt-2 mb-0 text-muted">Recherche en cours...</p>
                </div>
            `;
        }
    }

    // Fonction pour mettre à jour les résultats de recherche
    function updateSearchResults(data) {
        const resultsContainer = searchDropdown.querySelector('[data-simplebar]');
        if (!resultsContainer) return;

        // Vider les résultats actuels
        resultsContainer.innerHTML = '';

        // Si aucun résultat
        if (data.count === 0) {
            resultsContainer.innerHTML = `
                <div class="p-3 text-center">
                    <div class="avatar-sm mx-auto mb-3">
                        <div class="avatar-title bg-light text-primary rounded-circle">
                            <i class="ri-search-line font-size-18"></i>
                        </div>
                    </div>
                    <p class="mb-0 text-muted">Aucun résultat trouvé</p>
                </div>
            `;
            return;
        }

        // Ajouter les sections de résultats
        let html = '';

        // Utilisateurs
        if (data.results.users && data.results.users.length > 0) {
            html += `
                <div class="dropdown-header">
                    <h6 class="text-overflow text-muted mb-0 text-uppercase">Utilisateurs</h6>
                </div>
            `;

            data.results.users.forEach(user => {
                let roleIcon = 'ri-user-line';
                let roleBadge = '';
                
                if (user.role === 'admin') {
                    roleIcon = 'ri-admin-line';
                    roleBadge = '<span class="badge bg-soft-primary text-primary">Admin</span>';
                } else if (user.role === 'entreprise') {
                    roleIcon = 'ri-building-line';
                    roleBadge = '<span class="badge bg-soft-success text-success">Entreprise</span>';
                }

                html += `
                    <a href="${user.url}" class="dropdown-item notify-item py-2">
                        <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-xs">
                                    <div class="avatar-title bg-soft-primary text-primary rounded-circle">
                                        <i class="${roleIcon}"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="m-0">${user.name} ${roleBadge}</h6>
                                <span class="fs-11 mb-0 text-muted">${user.email}</span>
                            </div>
                        </div>
                    </a>
                `;
            });
        }

        // Projets
        if (data.results.projets && data.results.projets.length > 0) {
            html += `
                <div class="dropdown-header">
                    <h6 class="text-overflow text-muted mb-0 text-uppercase">Projets</h6>
                </div>
            `;

            data.results.projets.forEach(projet => {
                html += `
                    <a href="${projet.url}" class="dropdown-item notify-item py-2">
                        <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-xs">
                                    <div class="avatar-title bg-soft-info text-info rounded-circle">
                                        <i class="ri-projector-line"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="m-0">${projet.name}</h6>
                                <span class="fs-11 mb-0 text-muted">${projet.description}</span>
                            </div>
                        </div>
                    </a>
                `;
            });
        }

        // Tâches
        if (data.results.taches && data.results.taches.length > 0) {
            html += `
                <div class="dropdown-header">
                    <h6 class="text-overflow text-muted mb-0 text-uppercase">Tâches</h6>
                </div>
            `;

            data.results.taches.forEach(tache => {
                html += `
                    <a href="${tache.url}" class="dropdown-item notify-item py-2">
                        <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-xs">
                                    <div class="avatar-title bg-soft-warning text-warning rounded-circle">
                                        <i class="ri-task-line"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="m-0">${tache.name}</h6>
                                <span class="fs-11 mb-0 text-muted">${tache.description}</span>
                            </div>
                        </div>
                    </a>
                `;
            });
        }

        // Contacts
        if (data.results.contacts && data.results.contacts.length > 0) {
            html += `
                <div class="dropdown-header">
                    <h6 class="text-overflow text-muted mb-0 text-uppercase">Contacts</h6>
                </div>
            `;

            data.results.contacts.forEach(contact => {
                html += `
                    <a href="${contact.url}" class="dropdown-item notify-item py-2">
                        <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar-xs">
                                    <div class="avatar-title bg-soft-danger text-danger rounded-circle">
                                        <i class="ri-mail-line"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="m-0">${contact.name}</h6>
                                <span class="fs-11 mb-0 text-muted">${contact.description}</span>
                            </div>
                        </div>
                    </a>
                `;
            });
        }

        // Ajouter le lien "Voir tous les résultats"
        html += `
            <div class="text-center pt-3 pb-1">
                <a href="/search?query=${encodeURIComponent(lastQuery)}" class="btn btn-primary btn-sm">
                    Voir tous les résultats <i class="ri-arrow-right-line ms-1"></i>
                </a>
            </div>
        `;

        resultsContainer.innerHTML = html;
    }

    // Fonction pour vider les résultats de recherche
    function clearSearchResults() {
        const resultsContainer = searchDropdown.querySelector('[data-simplebar]');
        if (resultsContainer) {
            resultsContainer.innerHTML = `
                <div class="p-3 text-center">
                    <p class="mb-0 text-muted">Tapez au moins 2 caractères pour rechercher</p>
                </div>
            `;
        }
    }

    // Événement de saisie dans le champ de recherche
    searchInput.addEventListener('input', function() {
        const query = this.value.trim();
        
        // Afficher/masquer le bouton de fermeture
        if (query.length > 0) {
            searchCloseBtn.classList.remove('d-none');
        } else {
            searchCloseBtn.classList.add('d-none');
        }
        
        // Afficher le dropdown
        if (query.length > 0) {
            searchDropdown.classList.add('show');
        } else {
            searchDropdown.classList.remove('show');
        }
        
        // Délai avant de lancer la recherche
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            performSearch(query);
        }, searchDelay);
    });

    // Événement de clic sur le bouton de fermeture
    searchCloseBtn.addEventListener('click', function(e) {
        e.preventDefault();
        searchInput.value = '';
        searchCloseBtn.classList.add('d-none');
        searchDropdown.classList.remove('show');
        lastQuery = '';
    });

    // Fermer le dropdown quand on clique ailleurs
    document.addEventListener('click', function(e) {
        if (!searchInput.contains(e.target) && !searchDropdown.contains(e.target)) {
            searchDropdown.classList.remove('show');
        }
    });

    // Ouvrir le dropdown quand on clique dans le champ de recherche
    searchInput.addEventListener('click', function() {
        if (this.value.trim().length > 0) {
            searchDropdown.classList.add('show');
        }
    });
});
