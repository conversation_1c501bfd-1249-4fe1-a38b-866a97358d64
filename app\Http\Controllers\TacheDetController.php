<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Tache;
use App\Models\Projet;

class TacheDetController extends Controller
{
    /**
     * Affiche la liste des tâches
     */
    public function index()
    {
        $taches = Tache::with(['projet', 'personnel'])->get();
        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('taches.index', compact('taches'))->with('userTheme', $theme);
    }

    /**
     * Affiche les détails d'une tâche spécifique
     */
    public function show($id)
    {
        $tache = Tache::with(['projet', 'personnel', 'comments.user', 'comments.replies.user'])->findOrFail($id);

        // Compter le nombre total de commentaires (commentaires principaux + réponses)
        $commentCount = $tache->comments->count() + $tache->comments->sum(function($comment) {
            return $comment->replies->count();
        });

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('tache-detail', compact('tache', 'commentCount'))->with('userTheme', $theme);
    }

    /**
     * Affiche les détails d'un projet avec toutes ses tâches
     */
    public function showProjet($id)
    {
        $projet = Projet::with(['taches.personnel'])->findOrFail($id);

        // Vérifier si l'utilisateur est autorisé à voir ce projet
        // Si c'est une entreprise, elle ne peut voir que ses propres projets
        if (auth()->user()->role == 'entreprise' && $projet->user_id != auth()->id()) {
            // Rediriger vers la liste des projets avec un message d'erreur
            return redirect()->route('projects.list')
                ->with('error', 'Vous n\'êtes pas autorisé à accéder à ce projet.');
        }

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('projet-detail', compact('projet'))->with('userTheme', $theme);
    }
}