/* Styles pour le système d'avatar */

.avatar {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    position: relative;
    overflow: hidden;
    border: 2px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
}

.avatar:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* Tailles d'avatar */
.avatar-xs {
    width: 24px;
    height: 24px;
    font-size: 10px;
}

.avatar-sm {
    width: 32px;
    height: 32px;
    font-size: 12px;
}

.avatar-md {
    width: 40px;
    height: 40px;
    font-size: 14px;
}

.avatar-lg {
    width: 56px;
    height: 56px;
    font-size: 18px;
}

.avatar-xl {
    width: 72px;
    height: 72px;
    font-size: 24px;
}

/* Initiales dans l'avatar */
.avatar-initials {
    line-height: 1;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Avatar avec bordure */
.avatar-bordered {
    border: 3px solid #fff;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

/* Avatar avec statut en ligne */
.avatar-online {
    position: relative;
}

.avatar-online::after {
    content: '';
    position: absolute;
    bottom: 0;
    right: 0;
    width: 25%;
    height: 25%;
    background-color: #28a745;
    border: 2px solid #fff;
    border-radius: 50%;
}

/* Avatar avec statut hors ligne */
.avatar-offline::after {
    background-color: #6c757d;
}

/* Avatar avec statut occupé */
.avatar-busy::after {
    background-color: #dc3545;
}

/* Avatar avec statut absent */
.avatar-away::after {
    background-color: #ffc107;
}

/* Groupe d'avatars */
.avatar-group {
    display: flex;
    align-items: center;
}

.avatar-group .avatar {
    margin-left: -8px;
    border: 2px solid #fff;
    position: relative;
    z-index: 1;
}

.avatar-group .avatar:first-child {
    margin-left: 0;
}

.avatar-group .avatar:hover {
    z-index: 2;
}

/* Avatar avec image de profil */
.avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 50%;
}

/* Avatar carré */
.avatar-square {
    border-radius: 8px;
}

.avatar-square img {
    border-radius: 8px;
}

/* Avatar avec gradient */
.avatar-gradient {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Animation de pulsation */
.avatar-pulse {
    animation: avatarPulse 2s infinite;
}

@keyframes avatarPulse {
    0% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0.7);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(102, 126, 234, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(102, 126, 234, 0);
    }
}

/* Avatar dans les listes */
.list-group-item .avatar {
    margin-right: 12px;
}

/* Avatar dans les cartes */
.card-header .avatar {
    margin-right: 8px;
}

/* Avatar dans la navbar */
.navbar .avatar {
    cursor: pointer;
}

/* Avatar dans les commentaires */
.comment .avatar {
    margin-right: 10px;
    flex-shrink: 0;
}

/* Avatar dans les notifications */
.notification-item .avatar {
    margin-right: 12px;
    flex-shrink: 0;
}

/* Responsive */
@media (max-width: 768px) {
    .avatar-lg {
        width: 48px;
        height: 48px;
        font-size: 16px;
    }
    
    .avatar-xl {
        width: 60px;
        height: 60px;
        font-size: 20px;
    }
}

/* Avatar avec tooltip */
.avatar[data-bs-toggle="tooltip"] {
    cursor: pointer;
}

/* Avatar en mode sombre */
@media (prefers-color-scheme: dark) {
    .avatar {
        border-color: rgba(255, 255, 255, 0.1);
    }
    
    .avatar-bordered {
        border-color: #343a40;
        box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
    }
}
