<?php $__env->startSection('contenu'); ?>
<div class="card-body p-4">
    <div class="text-center mt-2">
        <h5 class="text-primary fw-bold mb-4 animate__animated animate__fadeInDown"><?php echo e(__('auth.Welcome Back!')); ?></h5>
    </div>

    <div class="p-2 mt-4">
        <form method="POST" action="<?php echo e(route('login')); ?>" class="animate__animated animate__fadeIn animate__delay-1s">
            <?php echo csrf_field(); ?>

            <div class="mb-4">
                <div class="form-floating">
                    <input id="email" type="email" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="email" value="<?php echo e(old('email')); ?>" required autocomplete="email" autofocus placeholder="<?php echo e(__('auth.Email Address')); ?>">
                    <label for="email"><?php echo e(__('auth.Email Address')); ?></label>
                    <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback">
                            <?php echo e($message); ?>

                        </div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <div class="mb-4">
                <div class="form-floating">
                    <input id="password" type="password" class="form-control <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="password" required autocomplete="current-password" placeholder="<?php echo e(__('auth.Password')); ?>">
                    <label for="password"><?php echo e(__('auth.Password')); ?></label>
                    <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                        <div class="invalid-feedback">
                            <?php echo e($message); ?>

                        </div>
                    <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                </div>
            </div>

            <div class="form-check mb-4">
                <input class="form-check-input" type="checkbox" name="remember" id="remember" <?php echo e(old('remember') ? 'checked' : ''); ?>>
                <label class="form-check-label" for="remember">
                    <?php echo e(__('auth.Remember Me')); ?>

                </label>
            </div>

            <div class="mb-4">
                <button type="submit" class="btn btn-primary w-100 waves-effect waves-light hover-shadow">
                    <i class="ri-login-box-line align-middle me-1"></i> <?php echo e(__('auth.Login')); ?>

                </button>
            </div>

            <div class="mt-4 text-center">
                <?php if(Route::has('password.request')): ?>
                    <a class="text-muted" href="<?php echo e(route('password.request')); ?>">
                        <i class="ri-lock-password-line align-middle"></i> <?php echo e(__('auth.Forgot Your Password?')); ?>

                    </a>
                <?php endif; ?>
            </div>
        </form>
    </div>

    <div class="mt-4 text-center">
        <div class="signin-other-title">
            <h5 class="fs-13 mb-4 title"><?php echo e(__('auth.Sign In with')); ?></h5>
        </div>
        <div>
            <a href="<?php echo e(url('auth/google')); ?>" class="btn btn-soft-info w-100 hover-shadow">
                <i class="ri-google-fill align-middle me-1"></i> <?php echo e(__('message.login using google')); ?>

            </a>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('logintheme', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\task\resources\views/auth/login.blade.php ENDPATH**/ ?>