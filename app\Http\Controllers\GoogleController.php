<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Lara<PERSON>\Socialite\facades\Socialite;
use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Exception;


class GoogleController extends Controller
{
public function googlepage(){
    return Socialite::driver('google')
    ->with(['prompt' => 'consent select_account']) 
    ->redirect();
}

public function googlecallback()
{
    try {
        $user = Socialite::driver('google')->user();

        $finduser = User::where('email', $user->getEmail())->first();

        if ($finduser) {
            Auth::login($finduser);
        } else {
            $newUser = User::create([
                'name' => $user->getName(),
                'email' => $user->getEmail(),
                'google_id' => $user->getId(),
                'password' => bcrypt('random_password') 
            ]);

            Auth::login($newUser);
        }

        return redirect('/dashboard'); // Redirige après connexion
    } catch (Exception $e) {
        return redirect('/login')->with('error', 'Erreur de connexion avec Google');
    }
}

}