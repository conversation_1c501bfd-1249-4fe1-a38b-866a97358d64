@extends($userTheme ?? 'theme')

@section('contenu')
<div class="container-fluid">
    <div class="row">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ __('message.Edit Task_edit') }}</h4>
        </div>
    </div>

    <form action="/modifier-tache" method="POST">
        @csrf
        <input type="hidden" name="id" value="{{ $data->id }}">

        <div class="mb-3">
            <label>{{ __('message.Name_edit') }}</label>
            <input type="text" name="nom" class="form-control" value="{{ old('nom', $data->nom) }}">
        </div>
        <div class="mb-3">
            <label>{{ __('message.Description_edit') }}</label>
            <textarea name="description" class="form-control">{{ old('description', $data->description) }}</textarea>
        </div>
        <div class="mb-3">
            <label>{{ __('message.Deadline_edit') }}</label>
            <input type="date" name="deadline" class="form-control" value="{{ old('deadline', $data->deadline) }}">
        </div>
        <div class="mb-3">
            <label>{{ __('message.Status_edit') }}</label>
            <select name="statut" class="form-select">
                @foreach(['to_do', 'doing', 'bug', 'done'] as $s)
                    <option value="{{ $s }}" {{ old('statut', $data->statut) == $s ? 'selected' : '' }}>{{ ucfirst($s) }}</option>
                @endforeach
            </select>
        </div>
        <div class="mb-3">
            <label>{{ __('message.Progress (%)_edit') }}</label>
            <select name="avancement" class="form-select">
                @foreach([0, 20, 40, 60, 80, 100] as $a)
                    <option value="{{ $a }}" {{ old('avancement', $data->avancement) == $a ? 'selected' : '' }}>{{ $a }}%</option>
                @endforeach
            </select>
        </div>
        <div class="mb-3">
            <label>{{ __('message.Project_edit') }}</label>
            <select name="projet_id" class="form-select">
                @foreach($projets as $projet)
                    <option value="{{ $projet->id }}" {{ old('projet_id', $data->projet_id) == $projet->id ? 'selected' : '' }}>{{ $projet->project_name }}</option>
                @endforeach
            </select>
        </div>
        <div class="mb-3">
            <label>{{ __('message.Assigned to_edit') }}</label>
            <select name="personnel_id" class="form-select">
                @foreach($personnels as $personnel)
                    <option value="{{ $personnel->id }}" {{ old('personnel_id', $data->personnel_id) == $personnel->id ? 'selected' : '' }}>
                        {{ $personnel->name }}
                    </option>
                @endforeach
            </select>
        </div>
        <button class="btn btn-primary">{{ __('message.Update_edit') }}</button>
    </form>
</div>
@endsection
