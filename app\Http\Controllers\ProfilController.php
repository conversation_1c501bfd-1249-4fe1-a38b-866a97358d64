<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProfilController extends Controller
{
    public function profilUser()
    {
        // Récupérer l'utilisateur connecté
        $user = Auth::user();

        // Passer les données à la vue
        return view('profil', compact('user'));
    }

    public function editProfil()
    {
        $user = Auth::user();
        return view('edit-profil', compact('user'));
    }

    public function updateProfil(Request $request)
    {
        // Récupérer l'utilisateur connecté
        $user = Auth::user();

        // Valider les données
        $request->validate([
            'firstname' => 'required|string|max:255',
        ]);

        // Mettre à jour les informations de l'utilisateur
        $user->name = $request->firstname;
        $user->save();

        // Rediriger avec un message de succès
        return redirect()->route('profil')->with('success', 'Profil mis à jour avec succès !');
    }
}
