@extends($userTheme ?? 'theme')

@section('contenu')
<div class="page-content">
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
                    <h4 class="mb-sm-0">
                        <i class="ri-task-line text-primary me-2"></i>{{ __('message.Task Management') }}
                    </h4>
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">{{ __('message.TaskFlow') }}</a></li>
                            <li class="breadcrumb-item active">{{ __('message.Tasks') }}</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <!-- Alerts -->
        <div class="row">
            <div class="col-lg-12">
                @if(session('message'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="ri-check-double-line me-1 align-middle"></i> {{ session('message') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="ri-error-warning-line me-1 align-middle"></i> {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">{{ __('message.Task List') }}</h5>
                        <div class="flex-shrink-0">
                            <div class="d-flex gap-2">
                                <a href="/ajout-tache" class="btn btn-primary">
                                    <i class="ri-add-line align-bottom me-1"></i> {{ __('message.New Task') }}
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive table-card">
                            <table class="table table-nowrap table-striped align-middle mb-0">
                                <thead class="table-light text-muted">
                                    <tr>
                                        <th scope="col">{{ __('message.Task') }}</th>
                                        <th scope="col">{{ __('message.Project_task') }}</th>
                                        <th scope="col">{{ __('message.Deadline_task') }}</th>
                                        <th scope="col">{{ __('message.Assigned to_task') }}</th>
                                        <th scope="col">{{ __('message.Status_task') }}</th>
                                        <th scope="col">{{ __('message.Progress_task') }}</th>
                                        <th scope="col" style="width: 150px;">{{ __('message.Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($taches as $tache)
                                        <tr>
                                            <td>
                                                <div class="d-flex">
                                                    <div class="flex-grow-1">
                                                        <h5 class="fs-14 mb-1">
                                                            <a href="{{ route('tache-detail', ['id' => $tache->id]) }}" class="text-dark">{{ $tache->nom }}</a>
                                                        </h5>
                                                        <p class="text-muted mb-0" style="max-width: 200px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                                            {{ $tache->description ?: __('message.No description') }}
                                                        </p>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <a href="{{ route('projet-detail', ['id' => $tache->projet->id]) }}" class="text-primary">
                                                    {{ $tache->projet->project_name }}
                                                </a>
                                            </td>
                                            <td>
                                                @php
                                                    $deadline = \Carbon\Carbon::parse($tache->deadline);
                                                    $today = \Carbon\Carbon::today();
                                                    $diffInDays = $today->diffInDays($deadline, false);
                                                @endphp

                                                @if($diffInDays < 0)
                                                    <span class="badge bg-danger-subtle text-danger">
                                                        <i class="ri-time-line align-bottom"></i> {{ __('message.Late by') }} {{ abs($diffInDays) }} {{ __('message.day(s)') }}
                                                    </span>
                                                @elseif($diffInDays == 0)
                                                    <span class="badge bg-warning-subtle text-warning">
                                                        <i class="ri-time-line align-bottom"></i> {{ __('message.Today') }}
                                                    </span>
                                                @elseif($diffInDays <= 2)
                                                    <span class="badge bg-warning-subtle text-warning">
                                                        <i class="ri-time-line align-bottom"></i> {{ __('message.In') }} {{ $diffInDays }} {{ __('message.day(s)') }}
                                                    </span>
                                                @else
                                                    <span class="badge bg-success-subtle text-success">
                                                        <i class="ri-calendar-line align-bottom"></i> {{ \Carbon\Carbon::parse($tache->deadline)->format('d/m/Y') }}
                                                    </span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($tache->personnel)
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-xs flex-shrink-0 me-2">
                                                            <span class="avatar-title bg-soft-info text-info rounded-circle fs-6">
                                                                {{ strtoupper(substr($tache->personnel->name, 0, 1)) }}
                                                            </span>
                                                        </div>
                                                        <div class="flex-grow-1">{{ $tache->personnel->name }}</div>
                                                    </div>
                                                @else
                                                    <span class="badge bg-secondary-subtle text-secondary">{{ __('message.Not assigned') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($tache->statut == 'to_do')
                                                    <span class="badge bg-secondary-subtle text-secondary">{{ __('message.To do') }}</span>
                                                @elseif($tache->statut == 'doing')
                                                    <span class="badge bg-warning-subtle text-warning">{{ __('message.In progress') }}</span>
                                                @elseif($tache->statut == 'bug')
                                                    <span class="badge bg-danger-subtle text-danger">{{ __('message.Bug') }}</span>
                                                @elseif($tache->statut == 'done')
                                                    <span class="badge bg-success-subtle text-success">{{ __('message.Finished') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 8px;">
                                                    @php
                                                        if ($tache->avancement < 25) {
                                                            $progressClass = 'bg-danger';
                                                        } elseif ($tache->avancement < 50) {
                                                            $progressClass = 'bg-warning';
                                                        } elseif ($tache->avancement < 75) {
                                                            $progressClass = 'bg-info';
                                                        } else {
                                                            $progressClass = 'bg-success';
                                                        }
                                                    @endphp
                                                    <div class="progress-bar {{ $progressClass }}" role="progressbar" style="width: {{ $tache->avancement }}%;" aria-valuenow="{{ $tache->avancement }}" aria-valuemin="0" aria-valuemax="100"></div>
                                                </div>
                                                <span class="text-muted mt-1 d-block small">{{ $tache->avancement }}%</span>
                                            </td>
                                            <td>
                                                <div class="dropdown">
                                                    <button class="btn btn-soft-secondary btn-sm dropdown" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                                                        <i class="ri-more-fill align-middle"></i>
                                                    </button>
                                                    <ul class="dropdown-menu dropdown-menu-end">
                                                        <li>
                                                            <a class="dropdown-item" href="{{ route('tache-detail', ['id' => $tache->id]) }}">
                                                                <i class="ri-eye-fill align-bottom me-2 text-muted"></i> {{ __('message.View Details') }}
                                                            </a>
                                                        </li>
                                                        <li>
                                                            <a class="dropdown-item" href="/modifier-tache/{{ $tache->id }}">
                                                                <i class="ri-pencil-fill align-bottom me-2 text-muted"></i> {{ __('message.Edit_task') }}
                                                            </a>
                                                        </li>
                                                        <li class="dropdown-divider"></li>
                                                        <li>
                                                            <a class="dropdown-item text-danger" href="javascript:void(0);" onclick="if(confirm('{{ __('message.Are you sure') }}')) window.location.href='/supprimer-tache/{{ $tache->id }}';">
                                                                <i class="ri-delete-bin-fill align-bottom me-2 text-danger"></i> {{ __('message.Delete_task') }}
                                                            </a>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="7" class="text-center">
                                                <div class="py-4">
                                                    <i class="ri-task-line display-5 text-muted"></i>
                                                    <h5 class="mt-2">{{ __('message.No task found') }}</h5>
                                                    <p class="text-muted">{{ __('message.Start by creating') }}</p>
                                                    <a href="/ajout-tache" class="btn btn-primary">
                                                        <i class="ri-add-line align-bottom me-1"></i> {{ __('message.Create a Task') }}
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
