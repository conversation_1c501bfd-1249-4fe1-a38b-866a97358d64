<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Foundation\Auth\AuthenticatesUsers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class LoginController extends Controller
{
    /*
    |--------------------------------------------------------------------------
    | Login Controller
    |--------------------------------------------------------------------------
    |
    | This controller handles authenticating users for the application and
    | redirecting them to your home screen. The controller uses a trait
    | to conveniently provide its functionality to your applications.
    |
    */

    use AuthenticatesUsers;

    /**
     * Where to redirect users after login.
     *
     * @var string
     */
    protected $redirectTo = '/home';

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('guest')->except('logout');
        $this->middleware('auth')->only('logout');
    }

    /**
     * The user has been authenticated.
     * Override the method from the AuthenticatesUsers trait
     * to redirect users based on their role and load the appropriate theme.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  mixed  $user
     * @return \Illuminate\Http\RedirectResponse
     */
    protected function authenticated(Request $request, $user)
    {
        if ($user->role == 'admin') {
            // Administrateur - utilise le thème 'theme'
            return redirect('/dashboard');
        } elseif ($user->role == 'entreprise') {
            // Entreprise - utilise le thème 'admintheme'
            return redirect('/dash-entreprise');
        } elseif ($user->role == 'client') {
            // Client - utilise le thème 'logintheme'
            return redirect('/dash-entreprise');
        } else {
            // Membre - utilise le thème 'persotheme'
            return redirect('/dash-personnel');
        }
    }
}
