<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-layout="vertical" data-topbar="light" data-sidebar="dark" data-sidebar-size="lg" data-sidebar-image="none" data-preloader="disable" data-theme="default" data-theme-colors="default">


<head>

    <meta charset="utf-8" />
    <title>TaskFlow - Gestion de Projets</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Application de gestion de projets et de tâches" name="description" />
    <meta content="TaskFlow" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="{{ asset('assets/images/favicon.ico') }}">

    <!--Swiper slider css-->
    <link href="{{ asset('assets/libs/swiper/swiper-bundle.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- Layout config Js -->
    <script src="{{ asset('assets/js/layout.js') }}"></script>
    <!-- Bootstrap Css -->
    <link href="{{ asset('assets/css/bootstrap.min.css') }}" rel="stylesheet" type="text/css" />
    <!-- Icons Css -->
    <link href="{{ asset('assets/css/icons.min.css') }}" rel="stylesheet" type="text/css" />
    <!-- App Css-->
    <link href="{{ asset('assets/css/app.min.css') }}" rel="stylesheet" type="text/css" />
    <!-- custom Css-->
    <link href="{{ asset('assets/css/custom.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- Animation CSS -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" rel="stylesheet" />

    <style>
        /* Animation styles */
        .fade-in-up {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.6s ease-out, transform 0.6s ease-out;
        }
        .fade-in-up.active {
            opacity: 1;
            transform: translateY(0);
        }

        .staggered-fade-in > * {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.4s ease-out, transform 0.4s ease-out;
        }

        .hover-scale {
            transition: transform 0.3s ease;
        }
        .hover-scale:hover {
            transform: scale(1.05);
        }

        .hover-shadow {
            transition: box-shadow 0.3s ease, transform 0.3s ease;
        }
        .hover-shadow:hover {
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
            transform: translateY(-5px);
        }

        .btn-pulse {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(220, 53, 69, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
            }
        }

        /* Carousel animation */
        .carousel-item {
            transition: transform 0.6s ease-in-out;
        }

        /* Hero section animation */
        .hero-title-animation {
            display: inline-block;
            position: relative;
        }

        .hero-title-animation::after {
            content: '';
            position: absolute;
            width: 100%;
            transform: scaleX(0);
            height: 2px;
            bottom: 0;
            left: 0;
            background-color: #0d6efd;
            transform-origin: bottom right;
            transition: transform 0.3s ease-out;
        }

        .hero-title-animation:hover::after {
            transform: scaleX(1);
            transform-origin: bottom left;
        }
    </style>

</head>

<body data-bs-spy="scroll" data-bs-target="#navbar-example">

    <!-- Begin page -->
    <div class="layout-wrapper landing">
        <nav class="navbar navbar-expand-lg navbar-landing fixed-top" id="navbar">
            <div class="container">
                <a class="navbar-brand" href="{{ url('/') }}">
                    <img src="{{ asset('assets/images/newnewnew1.png') }}" class="card-logo card-logo-dark" alt="logo dark" height="30">
                    <img src="{{ asset('assets/images/newnewnew1.png') }}" class="card-logo card-logo-light" alt="logo light" height="30">
                </a>
                <button class="navbar-toggler py-0 fs-20 text-body" type="button" data-bs-toggle="collapse" data-bs-target="#navbarSupportedContent" aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
                    <i class="mdi mdi-menu"></i>
                </button>

                <div class="collapse navbar-collapse" id="navbarSupportedContent">
    <ul class="navbar-nav mx-auto mt-2 mt-lg-0" id="navbar-example">
        <li class="nav-item">
            <a class="nav-link active" href="#hero">Accueil</a>
        </li>

        <li class="nav-item">
            <a class="nav-link" href="#features">Fonctionnalités</a>
        </li>

        <li class="nav-item">
            <a class="nav-link" href="#contact">Contact</a>
        </li>
    </ul>

    @auth
    <div>
        <a href="/dash-entreprise" class="btn btn-primary">
            Tableau de bord
        </a>
    </div>
    @else
    <div>
        <a href="{{ route('login') }}" class="btn btn-primary">
            Connexion
        </a>

        <a href="{{ route('register') }}" class="btn btn-primary">
            Inscription
        </a>
    </div>
    @endauth
                </div>
                <!-- end row -->
            </div>
            <!-- end container -->
        </nav>

        <!-- start hero section -->
        <section class="section pb-0 hero-section" id="hero">
            <div class="bg-overlay bg-overlay-pattern"></div>
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8 col-sm-10">
                        <div class="text-center mt-lg-5 pt-5 animate__animated animate__fadeIn">
                            <h1 class="display-6 fw-semibold mb-3 lh-base animate__animated animate__fadeInDown">
                                TaskFlow - La solution complète pour
                                <span class="text-primary hero-title-animation">gérer vos projets</span> efficacement
                            </h1>
                            <p class="lead text-muted lh-base animate__animated animate__fadeInUp animate__delay-1s">
                                TaskFlow est une application de gestion de projets qui vous permet de suivre vos tâches,
                                de collaborer avec votre équipe et d'atteindre vos objectifs plus rapidement.
                            </p>

                            <div class="d-flex gap-2 justify-content-center mt-4 animate__animated animate__fadeInUp animate__delay-2s">
                                <a href="{{ route('register') }}" class="btn btn-primary hover-scale">
                                    Commencer gratuitement <i class="ri-arrow-right-line align-middle ms-1"></i>
                                </a>
                                <a href="#features" class="btn btn-danger btn-pulse">
                                    Découvrir les fonctionnalités <i class="ri-eye-line align-middle ms-1"></i>
                                </a>
                            </div>
                        </div>

                        <div class="mt-4 mt-sm-5 pt-sm-5 mb-sm-n5 demo-carousel animate__animated animate__fadeInUp animate__delay-3s">
                            <div class="demo-img-patten-top d-none d-sm-block">
                                <img src="{{ asset('assets/images/landing/img-pattern.png') }}" class="d-block img-fluid" alt="pattern">
                            </div>
                            <div class="demo-img-patten-bottom d-none d-sm-block">
                                <img src="{{ asset('assets/images/landing/img-pattern.png') }}" class="d-block img-fluid" alt="pattern">
                            </div>
                            <div id="heroCarousel" class="carousel slide p-2 p-sm-4 m-sm-5" data-bs-ride="carousel">
                                <div class="carousel-inner shadow-lg p-2 p-sm-4 rounded">
                                    <div class="carousel-item active" data-bs-interval="3000">
                                        <img src="{{ asset('assets/images/dashboard.png') }}" class="d-block w-100" alt="dashboard">
                                    </div>
                                    <div class="carousel-item" data-bs-interval="3000">
                                        <img src="{{ asset('assets/images/projet.png') }}" class="d-block w-100" alt="projets">
                                    </div>
                                    <div class="carousel-item" data-bs-interval="3000">
                                        <img src="{{ asset('assets/images/dashboard.png') }}" class="d-block w-100" alt="tâches">
                                    </div>
                                    <div class="carousel-item" data-bs-interval="3000">
                                        <img src="{{ asset('assets/images/kanban.png') }}" class="d-block w-100" alt="kanban">
                                    </div>
                                </div>
                                <button class="carousel-control-prev" type="button" data-bs-target="#heroCarousel" data-bs-slide="prev">
                                    <span class="carousel-control-prev-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">Précédent</span>
                                </button>
                                <button class="carousel-control-next" type="button" data-bs-target="#heroCarousel" data-bs-slide="next">
                                    <span class="carousel-control-next-icon" aria-hidden="true"></span>
                                    <span class="visually-hidden">Suivant</span>
                                </button>
                                <div class="carousel-indicators">
                                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="0" class="active" aria-current="true" aria-label="Slide 1"></button>
                                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="1" aria-label="Slide 2"></button>
                                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="2" aria-label="Slide 3"></button>
                                    <button type="button" data-bs-target="#heroCarousel" data-bs-slide-to="3" aria-label="Slide 4"></button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end hero section -->

        <!-- start features section -->
        <section class="section" id="features">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="text-center mb-5 fade-in-up">
                            <h3 class="mb-3 fw-semibold">Fonctionnalités principales</h3>
                            <p class="text-muted mb-4">TaskFlow offre toutes les fonctionnalités dont vous avez besoin pour gérer vos projets efficacement.</p>
                        </div>
                    </div>
                </div>
                <div class="row staggered-fade-in">
                    <div class="col-lg-4 mb-4">
                        <div class="card shadow-lg hover-shadow h-100">
                            <div class="card-body d-flex flex-column">
                                <div class="avatar-sm icon-effect mb-4">
                                    <div class="avatar-title bg-transparent rounded-circle text-primary h1">
                                        <i class="ri-stack-line fs-36 animate__animated animate__heartBeat animate__infinite animate__slower"></i>
                                    </div>
                                </div>
                                <h5 class="card-title">Gestion de projets</h5>
                                <p class="text-muted fs-14 flex-grow-1">Créez et gérez facilement vos projets avec des délais, des priorités et des assignations claires.</p>
                                <div class="mt-3">
                                    <a href="#" class="btn btn-sm btn-soft-primary">En savoir plus</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 mb-4">
                        <div class="card shadow-lg hover-shadow h-100">
                            <div class="card-body d-flex flex-column">
                                <div class="avatar-sm icon-effect mb-4">
                                    <div class="avatar-title bg-transparent rounded-circle text-primary h1">
                                        <i class="ri-list-check fs-36 animate__animated animate__heartBeat animate__infinite animate__slower animate__delay-1s"></i>
                                    </div>
                                </div>
                                <h5 class="card-title">Suivi des tâches</h5>
                                <p class="text-muted fs-14 flex-grow-1">Suivez l'avancement de vos tâches avec des statuts personnalisables et des notifications en temps réel.</p>
                                <div class="mt-3">
                                    <a href="#" class="btn btn-sm btn-soft-primary">En savoir plus</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 mb-4">
                        <div class="card shadow-lg hover-shadow h-100">
                            <div class="card-body d-flex flex-column">
                                <div class="avatar-sm icon-effect mb-4">
                                    <div class="avatar-title bg-transparent rounded-circle text-primary h1">
                                        <i class="ri-team-line fs-36 animate__animated animate__heartBeat animate__infinite animate__slower animate__delay-2s"></i>
                                    </div>
                                </div>
                                <h5 class="card-title">Gestion d'équipe</h5>
                                <p class="text-muted fs-14 flex-grow-1">Gérez votre équipe, assignez des tâches et collaborez efficacement sur vos projets.</p>
                                <div class="mt-3">
                                    <a href="#" class="btn btn-sm btn-soft-primary">En savoir plus</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Deuxième rangée de fonctionnalités -->
                <div class="row staggered-fade-in mt-4">
                    <div class="col-lg-4 mb-4">
                        <div class="card shadow-lg hover-shadow h-100">
                            <div class="card-body d-flex flex-column">
                                <div class="avatar-sm icon-effect mb-4">
                                    <div class="avatar-title bg-transparent rounded-circle text-primary h1">
                                        <i class="ri-bar-chart-grouped-line fs-36 animate__animated animate__heartBeat animate__infinite animate__slower animate__delay-3s"></i>
                                    </div>
                                </div>
                                <h5 class="card-title">Tableaux de bord</h5>
                                <p class="text-muted fs-14 flex-grow-1">Visualisez l'avancement de vos projets avec des graphiques et des statistiques en temps réel.</p>
                                <div class="mt-3">
                                    <a href="#" class="btn btn-sm btn-soft-primary">En savoir plus</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 mb-4">
                        <div class="card shadow-lg hover-shadow h-100">
                            <div class="card-body d-flex flex-column">
                                <div class="avatar-sm icon-effect mb-4">
                                    <div class="avatar-title bg-transparent rounded-circle text-primary h1">
                                        <i class="ri-notification-line fs-36 animate__animated animate__heartBeat animate__infinite animate__slower animate__delay-4s"></i>
                                    </div>
                                </div>
                                <h5 class="card-title">Notifications</h5>
                                <p class="text-muted fs-14 flex-grow-1">Restez informé des mises à jour importantes avec des notifications personnalisables.</p>
                                <div class="mt-3">
                                    <a href="#" class="btn btn-sm btn-soft-primary">En savoir plus</a>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 mb-4">
                        <div class="card shadow-lg hover-shadow h-100">
                            <div class="card-body d-flex flex-column">
                                <div class="avatar-sm icon-effect mb-4">
                                    <div class="avatar-title bg-transparent rounded-circle text-primary h1">
                                        <i class="ri-calendar-check-line fs-36 animate__animated animate__heartBeat animate__infinite animate__slower animate__delay-5s"></i>
                                    </div>
                                </div>
                                <h5 class="card-title">Planification</h5>
                                <p class="text-muted fs-14 flex-grow-1">Planifiez vos projets et tâches avec un calendrier intégré et des rappels automatiques.</p>
                                <div class="mt-3">
                                    <a href="#" class="btn btn-sm btn-soft-primary">En savoir plus</a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end features section -->

        <!-- start contact -->
        <section class="section" id="contact">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-lg-8">
                        <div class="text-center mb-5 fade-in-up">
                            <h3 class="mb-3 fw-semibold animate__animated animate__fadeInDown">Contactez-nous</h3>
                            <p class="text-muted animate__animated animate__fadeInUp animate__delay-1s">N'hésitez pas à nous contacter pour toute question ou demande d'information.</p>
                        </div>
                    </div>
                </div>

                <div class="row gy-4">
                    <div class="col-lg-4 fade-in-up">
                        <div class="card shadow-lg hover-shadow p-4">
                            <div class="mt-2 animate__animated animate__fadeInLeft">
                                <h5 class="fs-13 text-muted text-uppercase">Adresse :</h5>
                                <div class="ff-secondary fw-semibold">Technopole de la Manouba<br>Manouba CP 2010</div>
                            </div>
                            <div class="mt-4 animate__animated animate__fadeInLeft animate__delay-1s">
                                <h5 class="fs-13 text-muted text-uppercase">Email :</h5>
                                <div class="ff-secondary fw-semibold">
                                    <a href="mailto:<EMAIL>" class="text-reset text-decoration-underline"><EMAIL></a>
                                </div>
                            </div>
                            <div class="mt-4 animate__animated animate__fadeInLeft animate__delay-2s">
                                <h5 class="fs-13 text-muted text-uppercase">Téléphone :</h5>
                                <div class="ff-secondary fw-semibold">
                                    <a href="tel:+33123456789" class="text-reset text-decoration-underline">+216 23 693 207</a>
                                </div>
                            </div>

                            <div class="mt-4 pt-2">
                                <div class="row">
                                    <div class="col-4 text-center">
                                        <div class="avatar-sm mx-auto mb-2">
                                            <div class="avatar-title bg-soft-primary text-primary rounded-circle fs-18 hover-scale">
                                                <i class="ri-facebook-fill"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4 text-center">
                                        <div class="avatar-sm mx-auto mb-2">
                                            <div class="avatar-title bg-soft-info text-info rounded-circle fs-18 hover-scale">
                                                <i class="ri-twitter-fill"></i>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4 text-center">
                                        <div class="avatar-sm mx-auto mb-2">
                                            <div class="avatar-title bg-soft-danger text-danger rounded-circle fs-18 hover-scale">
                                                <i class="ri-linkedin-fill"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-8 fade-in-up">
                        <div class="card shadow-lg p-4 animate__animated animate__fadeInRight">
                            @if(session('message'))
                                <div class="alert alert-success alert-dismissible fade show animate__animated animate__bounceIn" role="alert">
                                    <i class="ri-checkbox-circle-line me-1 align-middle fs-16"></i>
                                    {{ session('message') }}
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif

                            @if ($errors->any())
                                <div class="alert alert-danger alert-dismissible fade show animate__animated animate__shakeX" role="alert">
                                    <i class="ri-error-warning-line me-1 align-middle fs-16"></i>
                                    <ul class="mb-0">
                                        @foreach ($errors->all() as $error)
                                            <li>{{ $error }}</li>
                                        @endforeach
                                    </ul>
                                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                </div>
                            @endif

                            <h5 class="card-title mb-4">Envoyez-nous un message</h5>
                            <form action="{{ route('contact.send') }}" method="POST" class="contact-form">
                                @csrf
                                <div class="row">
                                    <div class="col-lg-6">
                                        <div class="mb-4 form-floating">
                                            <input name="nom" id="nom" type="text" class="form-control bg-light border-light" placeholder="Votre nom">
                                            <label for="nom">Nom</label>
                                        </div>
                                    </div>
                                    <div class="col-lg-6">
                                        <div class="mb-4 form-floating">
                                            <input name="email" id="email" type="email" class="form-control bg-light border-light" placeholder="Votre email">
                                            <label for="email">Email</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="mb-4 form-floating">
                                            <input name="sujet" id="sujet" type="text" class="form-control bg-light border-light" placeholder="Sujet de votre message">
                                            <label for="sujet">Sujet</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12">
                                        <div class="mb-4 form-floating">
                                            <textarea name="message" id="message" style="height: 120px" class="form-control bg-light border-light" placeholder="Votre message"></textarea>
                                            <label for="message">Message</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-lg-12 text-end">
                                        <button type="submit" class="btn btn-primary hover-scale">
                                            <i class="ri-send-plane-fill me-1 align-middle"></i> Envoyer le message
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
        <!-- end contact -->

        <!-- start cta -->
        <section class="py-5 bg-primary position-relative">
            <div class="bg-overlay bg-overlay-pattern opacity-50"></div>
            <div class="container">
                <div class="row align-items-center gy-4">
                    <div class="col-sm fade-in-up">
                        <div class="animate__animated animate__fadeInLeft">
                            <h4 class="text-white mb-0 fw-semibold">Commencez à gérer vos projets efficacement avec TaskFlow</h4>
                        </div>
                    </div>
                    <!-- end col -->
                    <div class="col-sm-auto fade-in-up">
                        <div class="animate__animated animate__fadeInRight">
                            <a href="{{ route('register') }}" class="btn bg-gradient btn-danger btn-lg hover-scale animate__animated animate__pulse animate__infinite animate__slower">
                                <i class="ri-user-add-line align-middle me-1"></i> S'inscrire maintenant
                            </a>
                        </div>
                    </div>
                    <!-- end col -->
                </div>
                <!-- end row -->
            </div>
            <!-- end container -->
        </section>
        <!-- end cta -->

        <!-- Start footer -->
        <footer class="custom-footer bg-dark py-5 position-relative">
            <div class="container">
                <div class="row fade-in-up">
                    <div class="col-lg-4 mt-4">
                        <div class="animate__animated animate__fadeInUp">
                            <div>
                                <img src="{{ asset('assets/images/newnewnew1.png') }}" alt="logo light" height="30" class="hover-scale">
                            </div>
                            <div class="mt-4 fs-13">
                                <p>Application de gestion de projets et de tâches</p>
                                <p class="ff-secondary">TaskFlow vous permet de gérer efficacement vos projets, vos équipes et vos tâches en un seul endroit.</p>
                            </div>
                            <div class="mt-4">
                                <a href="{{ route('register') }}" class="btn btn-soft-light btn-sm hover-scale">
                                    Essayer gratuitement <i class="ri-arrow-right-line align-middle ms-1"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-7 ms-lg-auto">
                        <div class="row">
                            <div class="col-sm-4 mt-4">
                                <div class="animate__animated animate__fadeInUp animate__delay-1s">
                                    <h5 class="text-white mb-0">Entreprise</h5>
                                    <div class="text-muted mt-3">
                                        <ul class="list-unstyled ff-secondary footer-list">
                                            <li><a href="#reviews" class="hover-scale d-inline-block">À propos</a></li>
                                            <li><a href="#team" class="hover-scale d-inline-block">Équipe</a></li>
                                            <li><a href="#features" class="hover-scale d-inline-block">Fonctionnalités</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4 mt-4">
                                <div class="animate__animated animate__fadeInUp animate__delay-2s">
                                    <h5 class="text-white mb-0">Services</h5>
                                    <div class="text-muted mt-3">
                                        <ul class="list-unstyled ff-secondary footer-list">
                                            <li><a href="#services" class="hover-scale d-inline-block">Gestion de projets</a></li>
                                            <li><a href="#services" class="hover-scale d-inline-block">Suivi des tâches</a></li>
                                            <li><a href="#services" class="hover-scale d-inline-block">Collaboration d'équipe</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4 mt-4">
                                <div class="animate__animated animate__fadeInUp animate__delay-3s">
                                    <h5 class="text-white mb-0">Support</h5>
                                    <div class="text-muted mt-3">
                                        <ul class="list-unstyled ff-secondary footer-list">
                                            <li><a href="#contact" class="hover-scale d-inline-block">Contact</a></li>
                                            <li><a href="#contact" class="hover-scale d-inline-block">Assistance</a></li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="row text-center text-sm-start align-items-center mt-5 fade-in-up">
                    <div class="col-sm-6">
                        <div class="animate__animated animate__fadeInUp animate__delay-4s">
                            <p class="copy-rights mb-0">
                                <script> document.write(new Date().getFullYear()) </script> © TaskFlow - Tous droits réservés
                            </p>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="text-sm-end mt-3 mt-sm-0 animate__animated animate__fadeInUp animate__delay-4s">
                            <ul class="list-inline mb-0 footer-social-link">
                                <li class="list-inline-item">
                                    <a href="javascript: void(0);" class="avatar-xs d-block hover-scale">
                                        <div class="avatar-title rounded-circle bg-soft-primary text-primary">
                                            <i class="ri-facebook-fill"></i>
                                        </div>
                                    </a>
                                </li>
                                <li class="list-inline-item">
                                    <a href="javascript: void(0);" class="avatar-xs d-block hover-scale">
                                        <div class="avatar-title rounded-circle bg-soft-info text-info">
                                            <i class="ri-linkedin-fill"></i>
                                        </div>
                                    </a>
                                </li>
                                <li class="list-inline-item">
                                    <a href="javascript: void(0);" class="avatar-xs d-block hover-scale">
                                        <div class="avatar-title rounded-circle bg-soft-danger text-danger">
                                            <i class="ri-twitter-fill"></i>
                                        </div>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
        <!-- end footer -->


        <!--start back-to-top-->
        <button onclick="topFunction()" class="btn btn-danger btn-icon landing-back-top" id="back-to-top">
            <i class="ri-arrow-up-line"></i>
        </button>
        <!--end back-to-top-->

    </div>
    <!-- end layout wrapper -->


    <!-- JAVASCRIPT -->
    <script src="{{ asset('assets/libs/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('assets/libs/simplebar/simplebar.min.js') }}"></script>
    <script src="{{ asset('assets/libs/node-waves/waves.min.js') }}"></script>
    <script src="{{ asset('assets/libs/feather-icons/feather.min.js') }}"></script>
    <script src="{{ asset('assets/js/pages/plugins/lord-icon-2.1.0.js') }}"></script>
    <script src="{{ asset('assets/js/plugins.js') }}"></script>

    <!--Swiper slider js-->
    <script src="{{ asset('assets/libs/swiper/swiper-bundle.min.js') }}"></script>

    <!-- landing init -->
    <script src="{{ asset('assets/js/pages/landing.init.js') }}"></script>

    <!-- Animation scroll script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Fonction pour vérifier si un élément est visible dans la fenêtre
            function isElementInViewport(el) {
                var rect = el.getBoundingClientRect();
                return (
                    rect.top <= (window.innerHeight || document.documentElement.clientHeight) * 0.8 &&
                    rect.bottom >= 0
                );
            }

            // Fonction pour activer les animations au défilement
            function handleScrollAnimations() {
                // Animer les éléments fade-in-up
                document.querySelectorAll('.fade-in-up').forEach(function(element) {
                    if (isElementInViewport(element)) {
                        element.classList.add('active');
                    }
                });

                // Animer les éléments staggered-fade-in
                document.querySelectorAll('.staggered-fade-in').forEach(function(container) {
                    if (isElementInViewport(container)) {
                        Array.from(container.children).forEach(function(child, index) {
                            setTimeout(function() {
                                child.style.opacity = '1';
                                child.style.transform = 'translateY(0)';
                            }, index * 150);
                        });
                    }
                });
            }

            // Exécuter au chargement initial
            handleScrollAnimations();

            // Exécuter lors du défilement
            window.addEventListener('scroll', handleScrollAnimations);

            // Initialiser le carrousel Bootstrap
            var heroCarousel = document.getElementById('heroCarousel');
            if (heroCarousel) {
                var carousel = new bootstrap.Carousel(heroCarousel, {
                    interval: 3000,
                    wrap: true
                });
            }

            // Animation des formulaires
            const formInputs = document.querySelectorAll('.form-floating input, .form-floating textarea');
            formInputs.forEach(input => {
                input.addEventListener('focus', function() {
                    this.parentElement.classList.add('animate__animated', 'animate__headShake');
                    setTimeout(() => {
                        this.parentElement.classList.remove('animate__animated', 'animate__headShake');
                    }, 1000);
                });
            });
        });
    </script>
</body>


</html>
