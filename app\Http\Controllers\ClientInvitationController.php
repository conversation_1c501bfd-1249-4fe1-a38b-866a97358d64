<?php

namespace App\Http\Controllers;

use App\Models\ClientInvitation;
use App\Models\User;
use App\Mail\ClientInvitationMail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;

class ClientInvitation<PERSON>ontroller extends Controller
{
    /**
     * Envoie une invitation à un client potentiel
     */
    public function sendInvitation(Request $request)
    {
        // Validation des données du formulaire
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email|unique:client_invitations,email',
            'password' => 'required|string|min:6',
        ]);

        // Générer un token unique
        $token = Str::random(64);

        // Créer l'invitation avec une expiration de 48 heures
        $invitation = ClientInvitation::create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'password' => $validated['password'], // <PERSON><PERSON> en clair pour l'email, sera hashé lors de la création du compte
            'token' => $token,
            'invited_by' => auth()->id(),
            'expires_at' => now()->addHours(48),
        ]);

        // Envoyer l'email d'invitation
        Mail::to($validated['email'])->send(new ClientInvitationMail($invitation));

        // Retourner une réponse
        if ($request->ajax()) {
            return 'Invitation envoyée avec succès à ' . $validated['email'];
        }

        return redirect()->back()->with('success', 'Invitation envoyée avec succès à ' . $validated['email']);
    }

    /**
     * Confirme l'invitation et crée le compte client
     */
    public function confirmInvitation($token)
    {
        // Rechercher l'invitation par token
        $invitation = ClientInvitation::where('token', $token)->first();

        // Vérifier si l'invitation existe
        if (!$invitation) {
            return redirect('/login')->with('error', 'Invitation invalide ou expirée.');
        }

        // Vérifier si l'invitation a expiré
        if ($invitation->isExpired()) {
            return redirect('/login')->with('error', 'Cette invitation a expiré.');
        }

        // Vérifier si l'email est déjà utilisé
        if (User::where('email', $invitation->email)->exists()) {
            return redirect('/login')->with('error', 'Un compte avec cette adresse email existe déjà.');
        }

        // Créer le compte utilisateur
        $user = User::create([
            'name' => $invitation->name,
            'email' => $invitation->email,
            'password' => Hash::make($invitation->password), // Mot de passe temporaire
            'role' => 'client',
            'entreprise_id' => $invitation->invited_by, // Associer le client à l'entreprise qui l'a invité
        ]);

        // Supprimer l'invitation
        $invitation->delete();

        // Rediriger vers la page de définition de mot de passe
        return redirect()->route('client.show-set-password', ['user_id' => $user->id])
            ->with('success', 'Votre compte a été créé avec succès. Veuillez définir votre mot de passe.');
    }
}
