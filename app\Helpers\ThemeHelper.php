<?php

namespace App\Helpers;

use Illuminate\Support\Facades\Auth;

class ThemeHelper
{
    /**
     * Détermine le thème à utiliser en fonction du rôle de l'utilisateur
     *
     * @return string
     */
    public static function getUserTheme()
    {
        if (!Auth::check()) {
            return 'logintheme'; // Thème par défaut pour les utilisateurs non connectés
        }

        $user = Auth::user();
        
        switch ($user->role) {
            case 'admin':
                return 'theme';
            case 'entreprise':
                return 'admintheme';
            case 'client':
                return 'clientheme';
            case 'membre':
                return 'persotheme';
            default:
                return 'theme';
        }
    }
}
