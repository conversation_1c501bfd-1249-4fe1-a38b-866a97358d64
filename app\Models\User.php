<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'poste',
        'entreprise_id',
        'paiement',
        'avatar',
        'avatar_color',
    ];
    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function projets()
    {
        return $this->hasMany(Projet::class);
    }

    public function entreprise()
    {
        return $this->belongsTo(User::class, 'entreprise_id');
    }

    public function membres()
    {
        return $this->hasMany(User::class, 'entreprise_id');
    }

    /**
     * Get all comments written by the user.
     */
    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * Get all comment likes by the user.
     */
    public function commentLikes()
    {
        return $this->hasMany(CommentLike::class);
    }

    // Méthode getAvatarHtml supprimée pour stabilité

    // Méthodes avatar supprimées pour stabilité
}

