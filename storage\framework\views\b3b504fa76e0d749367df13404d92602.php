<!doctype html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" data-layout="vertical" data-topbar="light" data-sidebar="dark" data-sidebar-size="lg" data-sidebar-image="none" data-preloader="disable" data-theme="default" data-theme-colors="default">

<head>
    <meta charset="utf-8" />
    <title><?php echo $__env->yieldContent('title', 'TaskFlow'); ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="TaskFlow - Gestion de projets et de tâches">
    <meta name="author" content="TaskFlow">

    <!-- App favicon -->
    <link rel="shortcut icon" href="<?php echo e(asset('assets/images/favicon.ico')); ?>">

    <!-- Layout config Js -->
    <script src="<?php echo e(asset('assets/js/layout.js')); ?>"></script>

    <!-- Bootstrap Css -->
    <link href="<?php echo e(asset('assets/css/bootstrap.min.css')); ?>" rel="stylesheet" type="text/css" />

    <!-- Icons Css -->
    <link href="<?php echo e(asset('assets/css/icons.min.css')); ?>" rel="stylesheet" type="text/css" />

    <!-- App Css-->
    <link href="<?php echo e(asset('assets/css/app.min.css')); ?>" rel="stylesheet" type="text/css" />

    <!-- Custom Css-->
    <link href="<?php echo e(asset('assets/css/custom.min.css')); ?>" rel="stylesheet" type="text/css" />

    <!-- Responsive fixes CSS -->
    <link href="<?php echo e(asset('assets/css/responsive-fixes.css')); ?>" rel="stylesheet" type="text/css" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />

    <!-- Custom styles -->
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #4b79a1, #283e51);
            --secondary-gradient: linear-gradient(135deg, #f8f9fa, #e9ecef);
            --accent-gradient: linear-gradient(135deg, #FFD700, #FFA500);
        }

        .auth-one-bg {
            background-image: var(--primary-gradient);
            background-position: center;
            background-size: cover;
        }

        .auth-page-wrapper {
            min-height: 100vh;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .auth-page-content {
            flex-grow: 1;
            z-index: 2;
        }

        .card {
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .btn-primary, .bg-primary {
            background: var(--primary-gradient) !important;
            border: none;
        }

        .btn-primary:hover {
            opacity: 0.95;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .text-primary {
            color: #4b79a1 !important;
        }

        .custom-card-header {
            background: var(--primary-gradient);
            color: white;
            padding: 1.2rem;
            border-radius: 0.5rem 0.5rem 0 0;
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            color: white;
            transition: all 0.3s ease;
        }

        .feature-item {
            transition: all 0.3s ease;
            border-radius: 0.5rem;
        }

        .feature-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .custom-badge {
            background: var(--accent-gradient);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-weight: bold;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .auth-logo img {
                height: 30px;
            }

            .feature-item {
                padding: 0.75rem !important;
            }

            .feature-icon {
                width: 40px;
                height: 40px;
            }
        }
    </style>

    <?php echo $__env->yieldContent('custom_css'); ?>
</head>

<body>
    <div class="auth-page-wrapper">
        <!-- Auth page background -->
        <div class="auth-one-bg-position auth-one-bg" id="auth-particles">
            <div class="bg-overlay" style="background-color: rgba(0, 0, 0, 0.4);"></div>

            <div class="shape">
                <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1440 120">
                    <path d="M 0,36 C 144,53.6 432,123.2 720,124 C 1008,124.8 1296,56.8 1440,40L1440 140L0 140z"></path>
                </svg>
            </div>
        </div>

        <!-- Auth page content -->
        <div class="auth-page-content">
            <div class="container">
                <!-- Logo and tagline -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="text-center mt-sm-5 mb-4 text-white animate__animated animate__fadeIn">
                            <div>
                                <a href="/" class="d-inline-block auth-logo">
                                    <img src="<?php echo e(asset('assets/images/newnewnew.png')); ?>" alt="TaskFlow Logo" height="50">
                                </a>
                            </div>
                            <p class="mt-3 fs-15 fw-medium animate__animated animate__fadeIn animate__delay-1s">
                                " Le bon flow pour chaque tâche. "
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Main content area - dynamically sized based on page needs -->
                <?php if (! empty(trim($__env->yieldContent('full_content')))): ?>
                    <?php echo $__env->yieldContent('full_content'); ?>
                <?php else: ?>
                    <div class="row justify-content-center">
                        <div class="<?php echo $__env->yieldContent('card_width', 'col-md-8 col-lg-6 col-xl-5'); ?>">
                            <div class="card mt-4 overflow-hidden animate__animated animate__fadeIn animate__faster">
                                <?php if (! empty(trim($__env->yieldContent('card_header')))): ?>
                                    <div class="custom-card-header text-center">
                                        <?php echo $__env->yieldContent('card_header'); ?>
                                    </div>
                                <?php endif; ?>

                                <div class="card-body p-4">
                                    <?php if (! empty(trim($__env->yieldContent('card_title')))): ?>
                                        <div class="text-center mb-4">
                                            <?php echo $__env->yieldContent('card_title'); ?>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Flash messages -->
                                    <?php if(session('status')): ?>
                                        <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
                                            <i class="fas fa-check-circle me-1 align-middle"></i> <?php echo e(session('status')); ?>

                                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                        </div>
                                    <?php endif; ?>

                                    <?php if(session('error')): ?>
                                        <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
                                            <i class="fas fa-exclamation-circle me-1 align-middle"></i> <?php echo e(session('error')); ?>

                                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                        </div>
                                    <?php endif; ?>

                                    <?php if(session('message') || session('success')): ?>
                                        <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
                                            <i class="fas fa-check-circle me-1 align-middle"></i> <?php echo e(session('message') ?? session('success')); ?>

                                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Main content -->
                                    <div class="p-2">
                                        <?php echo $__env->yieldContent('contenu'); ?>
                                    </div>
                                </div>

                                <?php if (! empty(trim($__env->yieldContent('card_footer')))): ?>
                                    <div class="card-footer py-3 text-center" style="background-color: #f8f9fa; border-top: 1px solid rgba(0,0,0,0.05);">
                                        <?php echo $__env->yieldContent('card_footer'); ?>
                                    </div>
                                <?php endif; ?>
                            </div>

                            <?php if (! empty(trim($__env->yieldContent('below_card')))): ?>
                                <div class="mt-4 text-center">
                                    <?php echo $__env->yieldContent('below_card'); ?>
                                </div>
                            <?php else: ?>
                                <div class="mt-4 text-center">
                                    <p class="mb-0 bg-white text-dark py-2 px-4 rounded-pill d-inline-block shadow-sm fw-medium">
                                        <?php echo e(__('message.Don\'t have an account')); ?> ?
                                        <a href="/register" class="fw-bold text-primary ms-1">
                                            <?php echo e(__('message.Signup')); ?> <i class="ri-arrow-right-line align-middle"></i>
                                        </a>
                                    </p>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12 text-center text-white-50">
                        <p class="mb-0">&copy; <?php echo e(date('Y')); ?> TaskFlow. Tous droits réservés.</p>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- JAVASCRIPT -->
    <script src="<?php echo e(asset('assets/libs/bootstrap/js/bootstrap.bundle.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/simplebar/simplebar.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/node-waves/waves.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/feather-icons/feather.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/pages/plugins/lord-icon-2.1.0.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/plugins.js')); ?>"></script>

    <!-- Particles JS -->
    <script src="<?php echo e(asset('assets/libs/particles.js/particles.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/pages/particles.app.js')); ?>"></script>

    <!-- Password addon init -->
    <script src="<?php echo e(asset('assets/js/pages/password-addon.init.js')); ?>"></script>

    <?php echo $__env->yieldContent('scripts'); ?>
</body>


</html><?php /**PATH C:\laragon\www\task\resources\views/logintheme.blade.php ENDPATH**/ ?>