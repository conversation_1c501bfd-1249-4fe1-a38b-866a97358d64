<?php $__env->startSection('contenu'); ?>
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
                    <h4 class="mb-sm-0"><?php echo e(__('message.Project Details')); ?></h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="<?php echo e(route('projects.list')); ?>"><?php echo e(__('message.Projects_detail')); ?></a></li>
                            <li class="breadcrumb-item active"><?php echo e(__('message.Details_detail')); ?></li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <?php if(session('success')): ?>
            <div class="alert alert-success"><?php echo e(session('success')); ?></div>
        <?php endif; ?>

        <?php if(session('error')): ?>
            <div class="alert alert-danger"><?php echo e(session('error')); ?></div>
        <?php endif; ?>

        <div class="row">
            <!-- Informations du projet -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header bg-soft-primary">
                        <h5 class="card-title mb-0"><?php echo e(__('message.Project Information')); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h4><?php echo e($projet->project_name); ?></h4>
                            <?php if($projet->task_image): ?>
                                <div class="mt-3 mb-3">
                                    <img src="<?php echo e(asset('storage/' . $projet->task_image)); ?>" alt="Image du projet" class="img-fluid rounded" style="max-height: 200px;">
                                </div>
                            <?php endif; ?>
                        </div>

                        <?php if($projet->project_description): ?>
                            <div class="mb-3">
                                <h5><?php echo e(__('message.Description_detail')); ?></h5>
                                <p><?php echo e($projet->project_description); ?></p>
                            </div>
                        <?php endif; ?>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h5><?php echo e(__('message.Start Date_detail')); ?></h5>
                                <p><?php echo e(\Carbon\Carbon::parse($projet->start_date)->format('d/m/Y')); ?></p>
                            </div>
                            <div class="col-md-6">
                                <h5><?php echo e(__('message.End Date_detail')); ?></h5>
                                <p><?php echo e($projet->end_date ? \Carbon\Carbon::parse($projet->end_date)->format('d/m/Y') : __('message.Not defined')); ?></p>
                            </div>
                        </div>

                        <?php if($projet->members): ?>
                            <div class="mb-3">
                                <h5><?php echo e(__('message.Members')); ?></h5>
                                <p><?php echo e($projet->members); ?></p>
                            </div>
                        <?php endif; ?>

                        <div class="mb-3">
                            <h5><?php echo e(__('message.Progress_detail')); ?></h5>
                            <?php
                                $totalTaches = $projet->taches->count();
                                $tachesTerminees = $projet->taches->where('statut', 'done')->count();
                                $progression = $totalTaches > 0 ? round(($tachesTerminees / $totalTaches) * 100) : 0;
                            ?>
                            <div class="progress" style="height: 20px;">
                                <div class="progress-bar bg-success" role="progressbar" style="width: <?php echo e($progression); ?>%;"
                                    aria-valuenow="<?php echo e($progression); ?>" aria-valuemin="0" aria-valuemax="100">
                                    <?php echo e($progression); ?>%
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-between mt-4">
                            <div class="btn-group" role="group">
                                <a href="/modifier-projet/<?php echo e($projet->id); ?>" class="btn btn-warning">
                                    <i class="ri-edit-2-line align-bottom me-1"></i> <?php echo e(__('message.Edit_detail')); ?>

                                </a>
                                <a href="<?php echo e(route('entreprise.dashboard', ['projet_id' => $projet->id])); ?>" class="btn btn-info">
                                    <i class="ri-kanban-view align-bottom me-1"></i> Voir Kanban
                                </a>
                            </div>
                            <a href="<?php echo e(route('projects.list')); ?>" class="btn btn-secondary">
                                <i class="ri-arrow-left-line align-bottom me-1"></i> <?php echo e(__('message.Back_detail')); ?>

                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liste des tâches -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header bg-soft-primary d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0"><?php echo e(__('message.Project Tasks')); ?></h5>
                        <a href="/ajout-tache?projet_id=<?php echo e($projet->id); ?>" class="btn btn-sm btn-primary">
                            <i class="ri-add-line align-bottom me-1"></i> <?php echo e(__('message.New Task_detail')); ?>

                        </a>
                    </div>
                    <div class="card-body">
                        <?php if($projet->taches->count() > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover">
                                    <thead class="table-light">
                                        <tr>
                                            <th><?php echo e(__('message.Name_detail')); ?></th>
                                            <th><?php echo e(__('message.Deadline_detail')); ?></th>
                                            <th><?php echo e(__('message.Status_detail')); ?></th>
                                            <th><?php echo e(__('message.Progress_detail2')); ?></th>
                                            <th><?php echo e(__('message.Assigned to_detail')); ?></th>
                                            <th><?php echo e(__('message.Actions_detail')); ?></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php $__currentLoopData = $projet->taches; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $tache): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($tache->nom); ?></td>
                                            <td><?php echo e(\Carbon\Carbon::parse($tache->deadline)->format('d/m/Y')); ?></td>
                                            <td>
                                                <?php if($tache->statut == 'to_do'): ?>
                                                    <span class="badge bg-secondary"><?php echo e(__('message.To do_detail')); ?></span>
                                                <?php elseif($tache->statut == 'doing'): ?>
                                                    <span class="badge bg-warning"><?php echo e(__('message.In progress_detail')); ?></span>
                                                <?php elseif($tache->statut == 'bug'): ?>
                                                    <span class="badge bg-danger"><?php echo e(__('message.Bugs')); ?></span>
                                                <?php elseif($tache->statut == 'done'): ?>
                                                    <span class="badge bg-success"><?php echo e(__('message.Completed_detail')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="progress" style="height: 20px;">
                                                    <div class="progress-bar bg-primary" role="progressbar" style="width: <?php echo e($tache->avancement); ?>%;"
                                                        aria-valuenow="<?php echo e($tache->avancement); ?>" aria-valuemin="0" aria-valuemax="100">
                                                        <?php echo e($tache->avancement); ?>%
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if($tache->personnel): ?>
                                                    <?php echo e($tache->personnel->name); ?>

                                                <?php else: ?>
                                                    <span class="text-muted"><?php echo e(__('message.Not assigned_detail')); ?></span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <a href="<?php echo e(route('tache-detail', ['id' => $tache->id])); ?>" class="btn btn-sm btn-info">
                                                        <i class="ri-eye-fill"></i>
                                                    </a>
                                                    <a href="/modifier-tache/<?php echo e($tache->id); ?>" class="btn btn-sm btn-warning">
                                                        <i class="ri-edit-2-line"></i>
                                                    </a>
                                                    <a href="/supprimer-tache/<?php echo e($tache->id); ?>" class="btn btn-sm btn-danger"
                                                       onclick="return confirm('<?php echo e(__('message.Are you sure to delete')); ?>')">
                                                        <i class="ri-delete-bin-5-line"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php else: ?>
                            <div class="alert alert-info">
                                <?php echo e(__('message.No task associated')); ?>

                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Statistiques des tâches -->
                <div class="card mt-4">
                    <div class="card-header bg-soft-primary">
                        <h5 class="card-title mb-0"><?php echo e(__('message.Task Statistics')); ?></h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="card bg-light mb-3">
                                    <div class="card-body text-center">
                                        <h5 class="card-title"><?php echo e(__('message.Total tasks')); ?></h5>
                                        <h2 class="mt-3"><?php echo e($projet->taches->count()); ?></h2>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card bg-light mb-3">
                                    <div class="card-body text-center">
                                        <h5 class="card-title"><?php echo e(__('message.Completed tasks')); ?></h5>
                                        <h2 class="mt-3"><?php echo e($projet->taches->where('statut', 'done')->count()); ?></h2>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-3">
                                <div class="card bg-secondary-subtle mb-3">
                                    <div class="card-body text-center">
                                        <h6 class="card-title"><?php echo e(__('message.To do_detail')); ?></h6>
                                        <h3 class="mt-2"><?php echo e($projet->taches->where('statut', 'to_do')->count()); ?></h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-warning-subtle mb-3">
                                    <div class="card-body text-center">
                                        <h6 class="card-title"><?php echo e(__('message.In progress_detail')); ?></h6>
                                        <h3 class="mt-2"><?php echo e($projet->taches->where('statut', 'doing')->count()); ?></h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-danger-subtle mb-3">
                                    <div class="card-body text-center">
                                        <h6 class="card-title"><?php echo e(__('message.Bugs')); ?></h6>
                                        <h3 class="mt-2"><?php echo e($projet->taches->where('statut', 'bug')->count()); ?></h3>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card bg-success-subtle mb-3">
                                    <div class="card-body text-center">
                                        <h6 class="card-title"><?php echo e(__('message.Completed_detail')); ?></h6>
                                        <h3 class="mt-2"><?php echo e($projet->taches->where('statut', 'done')->count()); ?></h3>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>
<?php $__env->stopSection(); ?>
<?php echo $__env->make($userTheme ?? 'theme', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\task\resources\views/projet-detail.blade.php ENDPATH**/ ?>