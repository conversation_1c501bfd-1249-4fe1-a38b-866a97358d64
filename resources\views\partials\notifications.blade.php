@foreach($notifications as $notification)
    <div class="text-reset notification-item d-block dropdown-item position-relative {{ $notification->read ? '' : 'bg-light-subtle' }}">
        <div class="d-flex">
            <div class="avatar-xs me-3 flex-shrink-0">
                @if($notification->type == 'tache_created')
                    <span class="avatar-title bg-success-subtle text-success rounded-circle fs-16">
                        <i class="ri-task-line"></i>
                    </span>
                @elseif($notification->type == 'tache_updated')
                    <span class="avatar-title bg-info-subtle text-info rounded-circle fs-16">
                        <i class="ri-edit-box-line"></i>
                    </span>
                @elseif($notification->type == 'tache_assigned')
                    <span class="avatar-title bg-primary-subtle text-primary rounded-circle fs-16">
                        <i class="ri-user-follow-line"></i>
                    </span>
                @else
                    <span class="avatar-title bg-secondary-subtle text-secondary rounded-circle fs-16">
                        <i class="ri-notification-line"></i>
                    </span>
                @endif
            </div>
            <div class="flex-grow-1">
                <h6 class="mt-0 mb-1 fs-13 fw-semibold">{{ $notification->message }}</h6>
                @if($notification->tache_id)
                    <div class="mt-1">
                        <a href="{{ route('tache-detail', $notification->tache_id) }}" class="btn btn-sm btn-soft-primary">
                            <i class="ri-eye-line"></i> {{ __('message.View Task') }}
                        </a>
                        <button type="button" class="btn btn-sm btn-soft-success mark-as-read-btn" data-id="{{ $notification->id }}">
                            <i class="ri-check-double-line"></i> {{ __('message.Mark as read') }}
                        </button>
                    </div>
                @else
                    <div class="mt-1">
                        <button type="button" class="btn btn-sm btn-soft-success mark-as-read-btn" data-id="{{ $notification->id }}">
                            <i class="ri-check-double-line"></i> {{ __('message.Mark as read') }}
                        </button>
                    </div>
                @endif
                <p class="mb-0 fs-11 fw-medium text-uppercase text-muted">
                    <span><i class="mdi mdi-clock-outline"></i> {{ $notification->created_at->diffForHumans() }}</span>
                </p>
            </div>
            <div class="px-2 fs-15">
                <div class="form-check notification-check">
                    <input class="form-check-input" type="checkbox" value="" id="notification-check-{{ $notification->id }}" {{ $notification->read ? 'checked' : '' }}>
                    <label class="form-check-label" for="notification-check-{{ $notification->id }}"></label>
                </div>
            </div>
        </div>
    </div>
@endforeach

@if(count($notifications) == 0)
    <div class="text-center p-3">
        <h6 class="mb-0 fs-16 fw-semibold">{{ __('message.No notifications') }}</h6>
    </div>
@else
    <div class="my-3 text-center view-all">
        <a href="{{ route('notifications.all') }}" class="btn btn-soft-success waves-effect waves-light">
            {{ __('message.View all notifications') }} <i class="ri-arrow-right-line align-middle"></i>
        </a>
    </div>
@endif
