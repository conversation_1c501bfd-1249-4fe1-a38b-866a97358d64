@extends('admintheme')
@section('contenu')

<div class="page-content">
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
                    <h4 class="mb-sm-0">
                        <i class="ri-user-3-line text-primary me-2"></i>{{ __('message.Client Management') }}
                    </h4>
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">TaskFlow</a></li>
                            <li class="breadcrumb-item active">{{ __('message.Clients') }}</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row">
            <div class="col-lg-12">
                <div class="card shadow-lg border-0 animate__animated animate__fadeIn">
                    <div class="card-header d-flex align-items-center bg-light">
                        <h5 class="card-title mb-0 flex-grow-1">{{ __('message.Client List') }}</h5>
                        <div class="flex-shrink-0">
                            <button type="button" class="btn btn-primary animate__animated animate__pulse" data-bs-toggle="modal" data-bs-target="#emailModal">
                                <i class="ri-add-line align-bottom me-1"></i> {{ __('message.New Client') }}
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        @if(session('success'))
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                {{ session('success') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        @endif
                        
                        @if(session('error'))
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                {{ session('error') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        @endif
                        
                        <div class="table-responsive">
                            <table class="table table-hover table-striped align-middle mb-0 animate__animated animate__fadeIn">
                                <thead class="table-light">
                                    <tr>
                                        <th scope="col">{{ __('message.ID') }}</th>
                                        <th scope="col">{{ __('message.Name_list') }}</th>
                                        <th scope="col">{{ __('message.Email_list') }}</th>
                                        <th scope="col">{{ __('message.Registration Date') }}</th>
                                        <th scope="col">{{ __('message.Actions_detail') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($clients as $client)
                                        <tr>
                                            <td>{{ $client->id }}</td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    {!! $client->getAvatarHtml('sm', 'me-2') !!}
                                                    <div class="flex-grow-1">
                                                        <div class="fw-semibold">{{ $client->name }}</div>
                                                        <small class="text-muted">{{ ucfirst($client->role) }}</small>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>{{ $client->email }}</td>
                                            <td>{{ $client->created_at->format('d/m/Y') }}</td>
                                            <td>
                                                <div class="d-flex gap-2">
                                                    <a href="{{ route('clients.show', $client->id) }}" class="btn btn-sm btn-info">
                                                        <i class="ri-eye-line"></i> {{ __('message.View') }}
                                                    </a>
                                                    <form action="{{ route('clients.destroy', $client->id) }}" method="POST" onsubmit="return confirm('{{ __('message.Are you sure you want to delete this client?') }}')">
                                                        @csrf
                                                        @method('DELETE')
                                                        <button type="submit" class="btn btn-sm btn-danger">
                                                            <i class="ri-delete-bin-line"></i> {{ __('message.Delete') }}
                                                        </button>
                                                    </form>
                                                </div>
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="5" class="text-center">{{ __('message.No clients found') }}</td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal pour l'ajout d'un client -->
<div class="modal fade" id="emailModal" tabindex="-1" aria-labelledby="emailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailModalLabel">{{ __('message.Add New Client') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="/create-client" method="POST" id="emailForm">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">{{ __('message.Client Name') }}</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">{{ __('message.Email Address') }}</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">{{ __('message.Temporary Password') }}</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ __('message.Cancel') }}</button>
                    <button type="submit" class="btn btn-primary">{{ __('message.Send Invitation') }}</button>
                </div>
            </form>
        </div>
    </div>
</div>

@endsection
