<?php

namespace App\Http\Controllers;

use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Stripe\Stripe;
use Stripe\Checkout\Session;

class StripeController extends Controller
{
    public function checkout()
    {
        return view('checkout');
    }

    public function createCheckoutSession(Request $request)
    {
        Stripe::setApiKey(config('services.stripe.secret'));

        // Stocker l'ID de l'utilisateur dans la session pour le récupérer après le paiement
        session(['user_id_for_payment' => Auth::id()]);

        $session = Session::create([
            'payment_method_types' => ['card'],
            'line_items' => [[
                'price_data' => [
                    'currency' => 'eur',
                    'product_data' => [
                        'name' => 'Abonnement TaskFlow',
                        'description' => 'Accès complet à la plateforme TaskFlow',
                    ],
                    'unit_amount' => 2999, // 29.99€
                ],
                'quantity' => 1,
            ]],
            'mode' => 'payment',
            'success_url' => route('checkout.success'),
            'cancel_url' => route('checkout.cancel'),
        ]);

        return redirect($session->url);
    }

    public function success()
    {
        // Récupérer l'ID de l'utilisateur stocké dans la session
        $userId = session('user_id_for_payment');

        if ($userId) {
            // Mettre à jour le statut de paiement de l'utilisateur
            $user = User::find($userId);
            if ($user) {
                $user->paiement = 1; // Marquer comme payé
                $user->save();

                // Supprimer l'ID de la session
                session()->forget('user_id_for_payment');

                // Rediriger vers le tableau de bord
                return redirect('/dash-entreprise')->with('success', 'Paiement réussi ! Votre compte est maintenant activé.');
            }
        }

        // Fallback si l'utilisateur n'est pas trouvé
        return redirect('/dashboard')->with('success', 'Paiement réussi !');
    }

    public function cancel()
    {
        // Rediriger vers la page de paiement avec un message d'erreur
        return redirect('/checkout')->with('error', 'Le paiement a été annulé. Veuillez réessayer pour activer votre compte.');
    }
}