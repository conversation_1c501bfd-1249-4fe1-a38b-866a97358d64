

<?php $__env->startSection('contenu'); ?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="mb-sm-0"><?php echo e(__('message.Add Staff')); ?></h4>
            </div>
        </div>
    </div>

    <div class="card">
        <div class="card-header">
            <h4 class="card-title mb-0"><?php echo e(__('message.Add Staff')); ?></h4>
        </div>
        <div class="card-body">
            <?php if(session('success')): ?>
                <div class="alert alert-success">
                    <?php echo e(session('success')); ?>

                </div>
            <?php endif; ?>
            <?php if($errors->any()): ?>
                <div class="alert alert-danger">
                    <ul class="mb-0">
                        <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <li><?php echo e($error); ?></li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </ul>
                </div>
            <?php endif; ?>

            <form action="/ajoutpersonnel" method="POST">
                <?php echo csrf_field(); ?>



                <div class="mb-3">
                    <label><?php echo e(__('message.Name_staff')); ?></label>
                    <input type="text" class="form-control" name="nom" value="<?php echo e(old('nom')); ?>" required>
                </div>

                <div class="mb-3">
                    <label><?php echo e(__('message.Email_staff')); ?></label>
                    <input type="email" class="form-control" name="email" value="<?php echo e(old('email')); ?>" required>
                </div>

                <div class="mb-3">
                    <label><?php echo e(__('message.Password')); ?></label>
                    <input type="password" class="form-control" name="password" required>
                </div>

                <div class="mb-3">
                    <label><?php echo e(__('message.Confirm Password')); ?></label>
                    <input type="password" class="form-control" name="password_confirmation" required>
                </div>

                <div class="mb-3">
                    <label><?php echo e(__('message.Company_staff')); ?></label>
                    <select name="poste" class="form-select" required>
                        <option value=""><?php echo e(__('message.Select a position')); ?></option>
                        <option value="Développeur Frontend" <?php echo e(old('poste') == 'Développeur Frontend' ? 'selected' : ''); ?>>Développeur Frontend</option>
                        <option value="Développeur Backend" <?php echo e(old('poste') == 'Développeur Backend' ? 'selected' : ''); ?>>Développeur Backend</option>
                        <option value="Chef de projet" <?php echo e(old('poste') == 'Chef de projet' ? 'selected' : ''); ?>>Chef de projet</option>
                        <option value="Concepteur" <?php echo e(old('poste') == 'Concepteur' ? 'selected' : ''); ?>>Concepteur</option>
                        <option value="Testeur" <?php echo e(old('poste') == 'Testeur' ? 'selected' : ''); ?>>Testeur</option>
                        <option value="Designer UX/UI" <?php echo e(old('poste') == 'Designer UX/UI' ? 'selected' : ''); ?>>Designer UX/UI</option>
                    </select>
                </div>

                <button type="submit" class="btn btn-primary"><?php echo e(__('message.Add_staff')); ?></button>
            </form>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>


<?php echo $__env->make($userTheme ?? 'theme', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\task\resources\views/ajout-personnel.blade.php ENDPATH**/ ?>