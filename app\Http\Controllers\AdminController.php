<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use App\Models\User; // Utilisation du modèle User pour les admins

class AdminController extends Controller
{
    // Récupérer tous les admins
    public function getAdmin()
    {
        $admins = User::where('role', 'admin')->get(); // Filtrer les utilisateurs avec rôle 'admin'

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('liste-admin', ['data' => $admins])->with('userTheme', $theme);
    }

    // Modifier un admin
    public function updateAdmin(Request $req)
    {
        $admin = User::find($req->id); // Utilisation de User au lieu d'Admin
        if ($admin) {
            $admin->name = $req->name;
            $admin->email = $req->email;
            if ($req->password) {
                $admin->password = Hash::make($req->password); // Assurer que le mot de passe est hashé
            }
            $admin->save();
            return redirect('liste-admin')->with('message', 'Modification réussie');
        } else {
            return redirect('liste-admin')->with('error', 'Admin non trouvé');
        }
    }

    // Ajouter un nouvel admin
    public function addadmin(Request $req)
    {
        $req->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:6',
        ]);

        $admin = new User();
        $admin->name = $req->name;
        $admin->email = $req->email;
        $admin->password = Hash::make($req->password); // Ne jamais stocker un mot de passe en clair
        $admin->role = 'admin'; // Définir le rôle sur 'admin'
        $admin->save();

        return redirect('liste-admin')->with('message', 'Ajout d\'administrateur réussi');
    }

    // Récupérer un admin pour modification
    public function getAdminId($id)
    {
        $admin = User::find($id); // Utilisation de User au lieu de Admin

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('modifier-admin', ['data' => $admin])->with('userTheme', $theme);
    }

    // Supprimer un admin
    public function deleteAdmin($id)
    {
        $admin = User::find($id); // Utilisation de User au lieu de Admin
        if ($admin) {
            $admin->delete();
            return redirect('/liste-admin')->with('message', 'Un administrateur a été bien supprimé');
        } else {
            return redirect('/liste-admin')->with('error', 'Admin non trouvé');
        }
    }
}
