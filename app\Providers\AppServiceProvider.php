<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Définir la locale à partir de la session, avec fr comme valeur par défaut
        $locale = Session::get('locale', config('app.locale', 'fr'));
        App::setLocale($locale);

        // Observer et directives avatar supprimés pour stabilité
    }
}