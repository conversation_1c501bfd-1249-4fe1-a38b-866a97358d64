@extends($userTheme ?? 'theme')

@section('contenu')
<div class="container py-4">
    <!-- Alerte de succès -->
    @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="ri-check-double-line me-1 align-middle"></i> {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    <!-- En-tête de page -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url('/dashboard') }}">Tableau de bord</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('profil') }}">Mon profil</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Modifier le profil</li>
                </ol>
            </nav>
            <h2 class="mb-0">Modifier le profil</h2>
        </div>
    </div>

    <!-- Bannière de profil -->
    <div class="card mb-4 border-0 rounded-3 overflow-hidden">
        <div class="position-relative">
            <!-- Image de couverture -->
            <div style="height: 250px; background-image: url('{{ asset('assets/images/couverture.png') }}'); background-size: cover; background-position: center; position: relative;">
                <!-- Overlay pour améliorer la lisibilité du texte -->
                <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(rgba(0,0,0,0.1), rgba(0,0,0,0.5));"></div>
                <!-- Effet de vague en bas de l'image -->
                <div style="position: absolute; bottom: -2px; left: 0; width: 100%;">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 100" fill="#ffffff" preserveAspectRatio="none">
                        <path d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,100L1360,100C1280,100,1120,100,960,100C800,100,640,100,480,100C320,100,160,100,80,100L0,100Z"></path>
                    </svg>
                </div>
            </div>
        </div>
    </div>

    <!-- Informations de l'utilisateur -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body p-4">
            <div class="d-flex align-items-center">
                <!-- Avatar à gauche -->
                <div class="position-relative me-4">
                    <div class="position-relative">
                        {!! $user->getAvatarHtml('xl', 'img-thumbnail') !!}
                        @if($user->role == 'admin')
                            <span class="position-absolute bottom-0 end-0 badge rounded-pill bg-danger p-2" style="border: 2px solid white; box-shadow: 0 3px 10px rgba(0,0,0,0.1);"><i class="ri-admin-line"></i></span>
                        @elseif($user->role == 'entreprise')
                            <span class="position-absolute bottom-0 end-0 badge rounded-pill bg-primary p-2" style="border: 2px solid white; box-shadow: 0 3px 10px rgba(0,0,0,0.1);"><i class="ri-building-line"></i></span>
                        @elseif($user->role == 'membre')
                            <span class="position-absolute bottom-0 end-0 badge rounded-pill bg-info p-2" style="border: 2px solid white; box-shadow: 0 3px 10px rgba(0,0,0,0.1);"><i class="ri-user-line"></i></span>
                        @elseif($user->role == 'client')
                            <span class="position-absolute bottom-0 end-0 badge rounded-pill bg-success p-2" style="border: 2px solid white; box-shadow: 0 3px 10px rgba(0,0,0,0.1);"><i class="ri-user-star-line"></i></span>
                        @endif
                    </div>
                </div>

                <!-- Informations à droite -->
                <div>
                    <h2 class="mb-1 fw-bold text-primary">{{ $user->name }}</h2>
                    <div class="mb-2">
                        @if($user->role == 'admin')
                            <span class="badge bg-danger py-1 px-2">Administrateur</span>
                        @elseif($user->role == 'entreprise')
                            <span class="badge bg-primary py-1 px-2">Propriétaire d'entreprise</span>
                        @elseif($user->role == 'membre')
                            <span class="badge bg-info py-1 px-2">{{ $user->poste ?? 'Membre d\'équipe' }}</span>
                        @elseif($user->role == 'client')
                            <span class="badge bg-success py-1 px-2">Client</span>
                        @endif
                    </div>

                    <div class="d-flex align-items-center">
                        <i class="ri-mail-line me-2 text-primary"></i>
                        <span>{{ $user->email }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white">
            <ul class="nav nav-tabs card-header-tabs" id="profileTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="personalDetails-tab" data-bs-toggle="tab" data-bs-target="#personalDetails" type="button" role="tab" aria-controls="personalDetails" aria-selected="true">
                        <i class="ri-user-line align-middle me-1"></i> Informations personnelles
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="changePassword-tab" data-bs-toggle="tab" data-bs-target="#changePassword" type="button" role="tab" aria-controls="changePassword" aria-selected="false">
                        <i class="ri-lock-password-line align-middle me-1"></i> Changer le mot de passe
                    </button>
                </li>
            </ul>
        </div>
        <div class="card-body p-4">
            <div class="tab-content" id="profileTabsContent">
                <!-- Onglet Informations personnelles -->
                <div class="tab-pane fade show active" id="personalDetails" role="tabpanel" aria-labelledby="personalDetails-tab">
                    <form action="{{ route('edit-profil.post') }}" method="POST">
                        @csrf
                        <div class="row g-3">
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="firstnameInput" class="form-label">Nom complet</label>
                                    <input type="text" class="form-control" id="firstnameInput" name="firstname" placeholder="Entrez votre nom" value="{{ $user->name }}">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="emailInput" class="form-label">Email</label>
                                    <input type="email" class="form-control" id="emailInput" placeholder="Entrez votre email" value="{{ $user->email }}" readonly>
                                </div>
                            </div>

                            @if($user->role == 'membre' && $user->poste)
                            <div class="col-md-6">
                                <div class="form-group mb-3">
                                    <label for="posteInput" class="form-label">Poste</label>
                                    <input type="text" class="form-control" id="posteInput" name="poste" placeholder="Entrez votre poste" value="{{ $user->poste }}">
                                </div>
                            </div>
                            @endif

                            <div class="col-12 mt-4">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="{{ route('profil') }}" class="btn btn-light">Annuler</a>
                                    <button type="submit" class="btn btn-primary">Mettre à jour</button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Onglet Changement de mot de passe -->
                <div class="tab-pane fade" id="changePassword" role="tabpanel" aria-labelledby="changePassword-tab">
                    <form action="{{ route('edit-profil.post') }}" method="POST">
                        @csrf
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="oldpasswordInput" class="form-label">Ancien mot de passe*</label>
                                    <input type="password" class="form-control" id="oldpasswordInput" name="old_password" placeholder="Entrez votre mot de passe actuel">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="newpasswordInput" class="form-label">Nouveau mot de passe*</label>
                                    <input type="password" class="form-control" id="newpasswordInput" name="new_password" placeholder="Entrez votre nouveau mot de passe">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group mb-3">
                                    <label for="confirmpasswordInput" class="form-label">Confirmer le mot de passe*</label>
                                    <input type="password" class="form-control" id="confirmpasswordInput" name="confirm_password" placeholder="Confirmez votre mot de passe">
                                </div>
                            </div>
                            <div class="col-12 mt-4">
                                <div class="d-flex justify-content-end">
                                    <button type="submit" class="btn btn-success">Changer le mot de passe</button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <div class="mt-5">
                        <h5 class="mb-3">{{ __('message.Login Activity') }}</h5>
                        <div class="card bg-light border-0">
                            <div class="card-body">
                                <div class="d-flex align-items-center">
                                    <div class="flex-shrink-0">
                                        <i class="ri-login-circle-line fs-3 text-primary"></i>
                                    </div>
                                    <div class="flex-grow-1 ms-3">
                                        <h6 class="mb-1">{{ __('message.Recent Login') }}</h6>
                                        <p class="text-muted mb-0">{{ __('message.Your last login was') }}: {{ $user->updated_at->diffForHumans() }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Styles de base */
.container {
    max-width: 1140px;
}

body {
    background-color: #f8f9fa;
}

/* Styles pour les onglets */
.nav-tabs {
    border-bottom: none;
}

.nav-tabs .nav-link {
    color: #495057;
    border: none;
    padding: 1rem 1.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border-radius: 0;
    position: relative;
}

.nav-tabs .nav-link.active {
    color: #4361ee;
    background-color: transparent;
    border-bottom: 3px solid #4361ee;
}

.nav-tabs .nav-link:hover {
    color: #4361ee;
}

.nav-tabs .nav-link i {
    margin-right: 8px;
    font-size: 1.1rem;
}

/* Styles pour les cartes */
.card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    margin-bottom: 24px;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1) !important;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    padding: 1.25rem 1.5rem;
}

.card-body {
    padding: 1.5rem;
}

/* Styles pour les formulaires */
.form-control {
    padding: 0.75rem 1rem;
    border-radius: 8px;
    border: 1px solid #e2e8f0;
    transition: all 0.3s ease;
    font-size: 1rem;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.02);
}

.form-control:focus {
    border-color: #4361ee;
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.15);
    outline: none;
}

.form-control::placeholder {
    color: #a0aec0;
}

.form-label {
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #4a5568;
    font-size: 0.95rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

/* Animation pour les onglets */
.tab-pane {
    animation: fadeIn 0.4s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(15px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Styles pour les boutons */
.btn {
    padding: 0.6rem 1.2rem;
    font-weight: 600;
    border-radius: 8px;
    transition: all 0.3s ease;
    letter-spacing: 0.3px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.btn-sm {
    padding: 0.4rem 0.8rem;
    font-size: 0.875rem;
}

.btn-primary {
    background-color: #4361ee;
    border-color: #4361ee;
}

.btn-primary:hover {
    background-color: #3a56d4;
    border-color: #3a56d4;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(67, 97, 238, 0.3);
}

.btn-success {
    background-color: #10b981;
    border-color: #10b981;
}

.btn-success:hover {
    background-color: #0ea271;
    border-color: #0ea271;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(16, 185, 129, 0.3);
}

.btn-light {
    background-color: #fff;
    border-color: #e2e8f0;
    color: #4a5568;
}

.btn-light:hover {
    background-color: #f8f9fa;
    border-color: #cbd5e1;
    color: #1e293b;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* Styles pour les alertes */
.alert {
    border: none;
    border-radius: 12px;
    padding: 1rem 1.5rem;
    margin-bottom: 24px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

/* Styles pour les avatars */
.rounded-circle {
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
    border: 3px solid white;
}

/* Styles pour les textes */
h1, h2, h3, h4, h5, h6 {
    color: #1e293b;
    font-weight: 700;
}

.text-primary {
    color: #4361ee !important;
}

.text-muted {
    color: #64748b !important;
}

/* Styles pour les breadcrumbs */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item a {
    color: #4361ee;
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #64748b;
}

/* Styles spécifiques pour les formulaires d'édition */
.form-row {
    margin-bottom: 1.5rem;
}

.form-control:disabled, .form-control[readonly] {
    background-color: #f8f9fa;
    opacity: 0.7;
}

.badge {
    padding: 0.5em 0.75em;
    font-weight: 600;
    border-radius: 6px;
    letter-spacing: 0.3px;
}

.badge.bg-primary {
    background-color: #4361ee !important;
}

.badge.bg-success {
    background-color: #10b981 !important;
}

.badge.bg-danger {
    background-color: #ef4444 !important;
}

.badge.bg-info {
    background-color: #3b82f6 !important;
}

/* Styles pour les avatars dans le profil */
.avatar.img-thumbnail {
    border: 4px solid white;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    width: 120px;
    height: 120px;
}

.avatar.avatar-xl.img-thumbnail {
    width: 120px;
    height: 120px;
}

/* Animation pour les avatars */
.avatar:hover {
    transform: scale(1.05);
    transition: all 0.3s ease;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Animation des onglets
    const tabLinks = document.querySelectorAll('.nav-link');
    tabLinks.forEach(link => {
        link.addEventListener('click', function() {
            const tabId = this.getAttribute('data-bs-target');
            const tabPane = document.querySelector(tabId);

            if (tabPane) {
                tabPane.style.animation = 'none';
                setTimeout(() => {
                    tabPane.style.animation = 'fadeIn 0.3s ease';
                }, 10);
            }
        });
    });
});
</script>
@endsection
