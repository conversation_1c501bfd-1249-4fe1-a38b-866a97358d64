<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Blade;
use App\Models\User;
use App\Observers\UserObserver;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Définir la locale à partir de la session, avec fr comme valeur par défaut
        $locale = Session::get('locale', config('app.locale', 'fr'));
        App::setLocale($locale);

        // Enregistrer l'observer pour les utilisateurs
        User::observe(UserObserver::class);

        // Directive Blade pour les avatars simples
        Blade::directive('avatar', function ($expression) {
            return "<?php echo \App\Helpers\AvatarHelper::getAvatarHtml({$expression}); ?>";
        });

        // Directive Blade simplifiée pour les avatars d'utilisateur
        Blade::directive('userAvatar', function ($expression) {
            return "<?php
                \$user = {$expression};
                if (\$user && method_exists(\$user, 'getAvatarHtml')) {
                    echo \$user->getAvatarHtml('md', '');
                }
            ?>";
        });
    }
}