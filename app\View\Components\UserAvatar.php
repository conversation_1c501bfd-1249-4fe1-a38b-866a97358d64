<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;
use App\Models\User;

class UserAvatar extends Component
{
    public $user;
    public $size;
    public $classes;
    public $showName;
    public $showStatus;

    /**
     * Create a new component instance.
     */
    public function __construct(
        User $user = null,
        string $size = 'md',
        string $classes = '',
        bool $showName = false,
        bool $showStatus = false
    ) {
        $this->user = $user;
        $this->size = $size;
        $this->classes = $classes;
        $this->showName = $showName;
        $this->showStatus = $showStatus;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.user-avatar');
    }
}
