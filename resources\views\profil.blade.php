@extends($userTheme ?? 'theme')

@section('contenu')
<div class="container py-4">
    <!-- Alerte de succès -->
    @if(session('success'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="ri-check-double-line me-1 align-middle"></i> {{ session('success') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
    @endif

    <!-- En-tête de page -->
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ url('/dashboard') }}">Tableau de bord</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Mon profil</li>
                </ol>
            </nav>
            <h2 class="mb-0">Mon profil</h2>
        </div>
    </div>

    <!-- Bannière de profil -->
    <div class="card mb-3 border-0 rounded-3 overflow-hidden">
        <div class="position-relative">
            <!-- Image de couverture -->
            <div style="height: 180px; background-image: url('{{ asset('assets/images/couverture.png') }}'); background-size: cover; background-position: center; position: relative;">
                <!-- Overlay pour améliorer la lisibilité du texte -->
                <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(rgba(0,0,0,0.1), rgba(0,0,0,0.5));"></div>
                <!-- Effet de vague en bas de l'image -->
                <div style="position: absolute; bottom: -2px; left: 0; width: 100%;">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1440 100" fill="#ffffff" preserveAspectRatio="none">
                        <path d="M0,64L80,69.3C160,75,320,85,480,80C640,75,800,53,960,48C1120,43,1280,53,1360,58.7L1440,64L1440,100L1360,100C1280,100,1120,100,960,100C800,100,640,100,480,100C320,100,160,100,80,100L0,100Z"></path>
                    </svg>
                </div>
            </div>

            <!-- Bouton d'édition -->
            <div class="position-absolute top-0 end-0 p-2">
                <a href="{{ route('edit-profil') }}" class="btn btn-light btn-sm rounded-pill shadow-sm">
                    <i class="ri-edit-box-line align-bottom me-1"></i> Modifier le profil
                </a>
            </div>
        </div>
    </div>

    <!-- Informations de l'utilisateur -->
    <div class="card border-0 shadow-sm mb-3">
        <div class="card-body p-3">
            <div class="d-flex align-items-center">
                <!-- Avatar à gauche -->
                <div class="position-relative me-3">
                    <div class="position-relative">
                        <div class="avatar avatar-xl img-thumbnail" style="background-color: #007bff; color: white;" title="{{ $user->name }}">
                            <span class="avatar-initials">{{ strtoupper(substr($user->name, 0, 1)) }}</span>
                        </div>
                        @if($user->role == 'admin')
                            <span class="position-absolute bottom-0 end-0 badge rounded-pill bg-danger p-2" style="border: 2px solid white; box-shadow: 0 2px 5px rgba(0,0,0,0.1);"><i class="ri-admin-line"></i></span>
                        @elseif($user->role == 'entreprise')
                            <span class="position-absolute bottom-0 end-0 badge rounded-pill bg-primary p-2" style="border: 2px solid white; box-shadow: 0 2px 5px rgba(0,0,0,0.1);"><i class="ri-building-line"></i></span>
                        @elseif($user->role == 'membre')
                            <span class="position-absolute bottom-0 end-0 badge rounded-pill bg-info p-2" style="border: 2px solid white; box-shadow: 0 2px 5px rgba(0,0,0,0.1);"><i class="ri-user-line"></i></span>
                        @elseif($user->role == 'client')
                            <span class="position-absolute bottom-0 end-0 badge rounded-pill bg-success p-2" style="border: 2px solid white; box-shadow: 0 2px 5px rgba(0,0,0,0.1);"><i class="ri-user-star-line"></i></span>
                        @endif
                    </div>
                </div>

                <!-- Informations à droite -->
                <div>
                    <h3 class="mb-1 fw-bold text-primary">{{ $user->name }}</h3>
                    <div class="mb-2">
                        @if($user->role == 'admin')
                            <span class="badge bg-danger py-1 px-2">Administrateur</span>
                        @elseif($user->role == 'entreprise')
                            <span class="badge bg-primary py-1 px-2">Propriétaire d'entreprise</span>
                        @elseif($user->role == 'membre')
                            <span class="badge bg-info py-1 px-2">{{ $user->poste ?? 'Membre d\'équipe' }}</span>
                        @elseif($user->role == 'client')
                            <span class="badge bg-success py-1 px-2">Client</span>
                        @endif
                    </div>

                    <div class="d-flex flex-column flex-md-row gap-1 gap-md-3">
                        <div class="d-flex align-items-center">
                            <i class="ri-mail-line me-1 text-primary"></i>
                            <span>{{ $user->email }}</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <i class="ri-calendar-line me-1 text-primary"></i>
                            <span>Membre depuis: {{ $user->created_at->format('d M, Y') }}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Contenu principal -->
    <div class="card border-0 shadow-sm">
        <div class="card-header bg-white p-2">
            <ul class="nav nav-tabs card-header-tabs" id="profileTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active py-2" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab" aria-controls="overview" aria-selected="true">
                        <i class="ri-user-line align-middle me-1"></i> Infos
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link py-2" id="projects-tab" data-bs-toggle="tab" data-bs-target="#projects" type="button" role="tab" aria-controls="projects" aria-selected="false">
                        <i class="ri-projector-line align-middle me-1"></i> Projets
                    </button>
                </li>
                @if($user->role == 'entreprise')
                <li class="nav-item" role="presentation">
                    <button class="nav-link py-2" id="team-tab" data-bs-toggle="tab" data-bs-target="#team" type="button" role="tab" aria-controls="team" aria-selected="false">
                        <i class="ri-team-line align-middle me-1"></i> Équipe
                    </button>
                </li>
                @endif
                @if($user->role == 'client')
                <li class="nav-item" role="presentation">
                    <button class="nav-link py-2" id="tasks-tab" data-bs-toggle="tab" data-bs-target="#tasks" type="button" role="tab" aria-controls="tasks" aria-selected="false">
                        <i class="ri-task-line align-middle me-1"></i> Tâches
                    </button>
                </li>
                @endif
            </ul>
        </div>
        <div class="card-body p-3">
            <div class="tab-content" id="profileTabsContent">
                <!-- Onglet Informations personnelles -->
                <div class="tab-pane fade show active" id="overview" role="tabpanel" aria-labelledby="overview-tab">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <h5 class="mb-2">Informations du profil</h5>
                            <div class="table-responsive">
                                <table class="table table-borderless mb-0">
                                    <tbody>
                                        <tr>
                                            <th scope="row" style="width: 30%; padding: 0.5rem;">Nom complet:</th>
                                            <td style="padding: 0.5rem;">{{ $user->name }}</td>
                                        </tr>
                                        <tr>
                                            <th scope="row" style="padding: 0.5rem;">Email:</th>
                                            <td style="padding: 0.5rem;">{{ $user->email }}</td>
                                        </tr>
                                        <tr>
                                            <th scope="row" style="padding: 0.5rem;">Rôle:</th>
                                            <td style="padding: 0.5rem;">
                                                @if($user->role == 'admin')
                                                    Administrateur
                                                @elseif($user->role == 'entreprise')
                                                    Propriétaire d'entreprise
                                                @elseif($user->role == 'membre')
                                                    Membre d'équipe
                                                @elseif($user->role == 'client')
                                                    Client
                                                @endif
                                            </td>
                                        </tr>
                                        @if($user->role == 'membre' && $user->poste)
                                        <tr>
                                            <th scope="row" style="padding: 0.5rem;">Poste:</th>
                                            <td style="padding: 0.5rem;">{{ $user->poste }}</td>
                                        </tr>
                                        @endif
                                        <tr>
                                            <th scope="row" style="padding: 0.5rem;">Membre depuis:</th>
                                            <td style="padding: 0.5rem;">{{ $user->created_at->format('d M, Y') }}</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h5 class="mb-2">Activité récente</h5>
                            <div class="card border-0 bg-light">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="flex-shrink-0">
                                            <i class="ri-user-line fs-4 text-primary"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-2">
                                            <h6 class="mb-0">Profil mis à jour</h6>
                                            <p class="text-muted mb-0 small">Vos informations de profil ont été mises à jour avec succès</p>
                                        </div>
                                    </div>
                                    <div class="d-flex align-items-center">
                                        <div class="flex-shrink-0">
                                            <i class="ri-login-circle-line fs-4 text-primary"></i>
                                        </div>
                                        <div class="flex-grow-1 ms-2">
                                            <h6 class="mb-0">Activité de connexion</h6>
                                            <p class="text-muted mb-0 small">Votre dernière connexion était: {{ $user->updated_at->diffForHumans() }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Onglet Projets -->
                <div class="tab-pane fade" id="projects" role="tabpanel" aria-labelledby="projects-tab">
                    <h5 class="mb-2">Mes projets</h5>
                    <div class="row g-2">
                        @if(isset($projets) && is_countable($projets) && count($projets) > 0)
                            @foreach($projets as $projet)
                            <div class="col-md-6 col-xl-4 mb-2">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body p-3">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <h6 class="card-title mb-0">{{ $projet->nom }}</h6>
                                            <div class="dropdown">
                                                <a href="#" class="dropdown-toggle text-muted" id="dropdownMenuLink{{ $projet->id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="ri-more-2-fill"></i>
                                                </a>
                                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink{{ $projet->id }}">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ url('/dash-entreprise?projet_id=' . $projet->id) }}">
                                                            <i class="ri-eye-line align-middle me-1"></i> Voir
                                                        </a>
                                                    </li>
                                                    @if($user->role == 'entreprise')
                                                    <li>
                                                        <a class="dropdown-item" href="{{ url('/modifier-projet/' . $projet->id) }}">
                                                            <i class="ri-edit-2-line align-middle me-1"></i> Modifier
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ url('/supprimer-projet/' . $projet->id) }}" onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce projet?')">
                                                            <i class="ri-delete-bin-line align-middle me-1"></i> Supprimer
                                                        </a>
                                                    </li>
                                                    @endif
                                                </ul>
                                            </div>
                                        </div>
                                        <p class="card-text text-muted mb-2 small">{{ Str::limit($projet->description, 80) }}</p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <span class="badge bg-primary">{{ $projet->statut }}</span>
                                            <small class="text-muted">{{ $projet->created_at->format('d M, Y') }}</small>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-white border-top-0 p-2">
                                        <div class="d-flex gap-1">
                                            <a href="{{ url('/dash-entreprise?projet_id=' . $projet->id) }}" class="btn btn-sm btn-primary">
                                                <i class="ri-kanban-line align-middle me-1"></i> Kanban
                                            </a>
                                            <a href="{{ url('/liste-tache?projet_id=' . $projet->id) }}" class="btn btn-sm btn-outline-secondary">
                                                <i class="ri-list-check align-middle me-1"></i> Tâches
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        @else
                            <div class="col-12">
                                <div class="alert alert-info py-2" role="alert">
                                    Aucun projet trouvé
                                </div>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Onglet Équipe (pour les entreprises) -->
                @if($user->role == 'entreprise')
                <div class="tab-pane fade" id="team" role="tabpanel" aria-labelledby="team-tab">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <h5 class="mb-0">Mon équipe</h5>
                        <a href="{{ url('/ajouter-personnel') }}" class="btn btn-primary btn-sm">
                            <i class="ri-user-add-line align-middle me-1"></i> Ajouter
                        </a>
                    </div>
                    <div class="row g-2">
                        @if(isset($membres) && is_countable($membres) && count($membres) > 0)
                            @foreach($membres as $membre)
                            <div class="col-md-6 col-xl-4 mb-2">
                                <div class="card h-100 border-0 shadow-sm">
                                    <div class="card-body p-3">
                                        <div class="d-flex justify-content-between align-items-center mb-2">
                                            <div class="d-flex align-items-center">
                                                <div class="flex-shrink-0">
                                                    {!! $membre->getAvatarHtml('md', 'me-2') !!}
                                                </div>
                                                <div class="flex-grow-1 ms-2">
                                                    <h6 class="card-title mb-0">{{ $membre->name }}</h6>
                                                    <p class="text-muted mb-0 small">{{ $membre->poste ?? 'Membre d\'équipe' }}</p>
                                                </div>
                                            </div>
                                            <div class="dropdown">
                                                <a href="#" class="dropdown-toggle text-muted" id="dropdownMenuLink{{ $membre->id }}" data-bs-toggle="dropdown" aria-expanded="false">
                                                    <i class="ri-more-2-fill"></i>
                                                </a>
                                                <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink{{ $membre->id }}">
                                                    <li>
                                                        <a class="dropdown-item" href="{{ url('/modifier-personnel/' . $membre->id) }}">
                                                            <i class="ri-edit-2-line align-middle me-1"></i> Modifier
                                                        </a>
                                                    </li>
                                                    <li>
                                                        <a class="dropdown-item" href="{{ url('/supprimer-personnel/' . $membre->id) }}" onclick="return confirm('Êtes-vous sûr de vouloir supprimer ce membre?')">
                                                            <i class="ri-delete-bin-line align-middle me-1"></i> Supprimer
                                                        </a>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div>
                                            <p class="text-muted mb-1 small"><i class="ri-mail-line me-1"></i> {{ $membre->email }}</p>
                                            <p class="text-muted mb-0 small"><i class="ri-calendar-line me-1"></i> Depuis: {{ $membre->created_at->format('d M, Y') }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        @else
                            <div class="col-12">
                                <div class="alert alert-info py-2" role="alert">
                                    Aucun membre d'équipe trouvé
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
                @endif

                <!-- Onglet Tâches (pour les clients) -->
                @if($user->role == 'client')
                <div class="tab-pane fade" id="tasks" role="tabpanel" aria-labelledby="tasks-tab">
                    <h5 class="mb-2">Mes tâches</h5>
                    <div class="table-responsive">
                        <table class="table table-hover table-sm">
                            <thead>
                                <tr>
                                    <th scope="col" class="py-2">#</th>
                                    <th scope="col" class="py-2">Tâche</th>
                                    <th scope="col" class="py-2">Projet</th>
                                    <th scope="col" class="py-2">Statut</th>
                                    <th scope="col" class="py-2">Échéance</th>
                                    <th scope="col" class="py-2">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @if(isset($taches) && is_countable($taches) && count($taches) > 0)
                                    @foreach($taches as $tache)
                                    <tr>
                                        <th scope="row" class="py-2">{{ $loop->iteration }}</th>
                                        <td class="py-2">{{ Str::limit($tache->titre, 30) }}</td>
                                        <td class="py-2">{{ Str::limit($tache->projet->nom, 20) }}</td>
                                        <td class="py-2">
                                            @if($tache->statut == 'todo')
                                                <span class="badge bg-secondary">À faire</span>
                                            @elseif($tache->statut == 'doing')
                                                <span class="badge bg-primary">En cours</span>
                                            @elseif($tache->statut == 'done')
                                                <span class="badge bg-success">Terminé</span>
                                            @elseif($tache->statut == 'bug')
                                                <span class="badge bg-danger">Bug</span>
                                            @endif
                                        </td>
                                        <td class="py-2">{{ $tache->date_fin ? $tache->date_fin->format('d/m/Y') : 'N/A' }}</td>
                                        <td class="py-2">
                                            <a href="{{ url('/tache-detail/' . $tache->id) }}" class="btn btn-sm btn-outline-primary py-0 px-2">
                                                <i class="ri-eye-line align-middle"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    @endforeach
                                @else
                                    <tr>
                                        <td colspan="6" class="text-center py-2">Aucune tâche trouvée</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<style>
/* Styles optimisés pour l'adaptation à l'écran */
.container {
    max-width: 98%;
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* Styles pour les onglets */
.nav-tabs {
    border-bottom: none;
}

.nav-tabs .nav-link {
    color: #495057;
    border: none;
    padding: 0.5rem 1rem;
    font-weight: 600;
    transition: all 0.3s ease;
    border-radius: 0;
}

.nav-tabs .nav-link.active {
    color: #4361ee;
    background-color: transparent;
    border-bottom: 3px solid #4361ee;
}

.nav-tabs .nav-link i {
    margin-right: 5px;
    font-size: 1rem;
}

/* Styles pour les cartes */
.card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    margin-bottom: 1rem;
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

/* Styles pour les boutons */
.btn {
    border-radius: 6px;
    transition: all 0.3s ease;
    font-weight: 600;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.btn-primary {
    background-color: #4361ee;
    border-color: #4361ee;
}

.btn-primary:hover {
    background-color: #3a56d4;
    border-color: #3a56d4;
}

/* Styles pour les badges */
.badge {
    padding: 0.35em 0.65em;
    font-weight: 600;
    border-radius: 4px;
}

/* Animation pour les onglets */
.tab-pane {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

/* Styles pour les tableaux */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    color: #4a5568;
    border-bottom: 2px solid #e2e8f0;
    font-size: 0.8rem;
}

.table td {
    vertical-align: middle;
    border-bottom: 1px solid #e2e8f0;
    color: #4a5568;
}

/* Styles pour les breadcrumbs */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 0.5rem;
}

.breadcrumb-item a {
    color: #4361ee;
    text-decoration: none;
}

/* Styles pour les avatars dans le profil */
.avatar.img-thumbnail {
    border: 3px solid white;
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    width: 100px;
    height: 100px;
}

.avatar.avatar-xl.img-thumbnail {
    width: 100px;
    height: 100px;
}

/* Animation pour les avatars */
.avatar:hover {
    transform: scale(1.05);
    transition: all 0.3s ease;
}

/* Optimisations pour les écrans larges */
@media (min-width: 1200px) {
    .container {
        max-width: 95%;
    }
}

/* Optimisations pour les écrans moyens */
@media (min-width: 768px) and (max-width: 1199px) {
    .container {
        max-width: 98%;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Activation des tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Animation des onglets
    const tabLinks = document.querySelectorAll('.nav-link');
    tabLinks.forEach(link => {
        link.addEventListener('click', function() {
            const tabId = this.getAttribute('data-bs-target');
            const tabPane = document.querySelector(tabId);
            if (tabPane) {
                tabPane.style.animation = 'none';
                setTimeout(() => {
                    tabPane.style.animation = 'fadeIn 0.3s ease';
                }, 10);
            }
        });
    });
});
</script>
@endsection
