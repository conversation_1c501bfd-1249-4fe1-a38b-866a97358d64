<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Auth;
class HomeController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index()
    {
        //return view('home');
        if(Auth::user()->role=="admin"){
            //admin
            return redirect('/dashboard');
        }elseif(Auth::user()->role=="entreprise" || Auth::user()->role=="client"){
            //entreprise ou client
            return redirect('/dash-entreprise');
        }else{
            return redirect('/dash-personnel');
        }
    }
}
