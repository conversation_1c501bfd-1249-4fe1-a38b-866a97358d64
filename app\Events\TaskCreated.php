<?php

namespace App\Events;

use App\Models\Tache;
use App\Models\User;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class TaskCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    /**
     * La tâche qui a été créée.
     *
     * @var \App\Models\Tache
     */
    public $tache;

    /**
     * L'utilisateur qui a créé la tâche.
     *
     * @var \App\Models\User
     */
    public $user;

    /**
     * Create a new event instance.
     *
     * @param \App\Models\Tache $tache
     * @param \App\Models\User $user
     */
    public function __construct(Tache $tache, User $user)
    {
        $this->tache = $tache;
        $this->user = $user;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        // Diffuser sur un canal privé pour l'entreprise propriétaire du projet
        $projet = \App\Models\Projet::find($this->tache->projet_id);

        return [
            new PrivateChannel("user.{$projet->user_id}"),
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        return [
            'id' => $this->tache->id,
            'nom' => $this->tache->nom,
            'projet_id' => $this->tache->projet_id,
            'created_by' => $this->user->id,
        ];
    }
}
