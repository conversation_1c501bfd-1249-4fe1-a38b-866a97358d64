import './bootstrap';

// Charger les traductions depuis l'API
document.addEventListener('DOMContentLoaded', function() {
    fetch('/js/lang')
        .then(response => response.json())
        .then(data => {
            window.translations = data.messages;
            // V<PERSON> pouvez maintenant utiliser les traductions dans votre JavaScript
            console.log('Traductions chargées:', window.translations);

            // Mettre à jour les éléments de l'interface si nécessaire
            if (document.getElementById('menu-dashboard')) {
                document.getElementById('menu-dashboard').innerText = data.messages.dashboard;
            }

            // Mettre à jour d'autres éléments de l'interface
            updateUITranslations(data.messages);
        })
        .catch(error => console.error('Erreur lors du chargement des traductions:', error));
});

// Fonction pour mettre à jour les traductions dans l'interface
function updateUITranslations(messages) {
    // Mettre à jour les éléments avec des attributs data-translate
    document.querySelectorAll('[data-translate]').forEach(element => {
        const key = element.getAttribute('data-translate');
        if (messages[key]) {
            element.innerText = messages[key];
        }
    });
}
