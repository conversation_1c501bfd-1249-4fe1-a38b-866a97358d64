<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Personnel extends Model
{
    use HasFactory;

    // Définir les champs pouvant être assignés en masse
    protected $fillable = [
        'nom', 
        'prenom', 
        'email', 
        'poste'
    ];
      public function projets()
    {
        return $this->belongsToMany(Projet::class, 'project_personnel');
    }
}
