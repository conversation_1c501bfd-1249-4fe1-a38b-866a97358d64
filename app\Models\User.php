<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'poste',
        'entreprise_id',
        'paiement',
        'avatar',
        'avatar_color',
    ];
    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'password' => 'hashed',
    ];

    public function projets()
    {
        return $this->hasMany(Projet::class);
    }

    public function entreprise()
    {
        return $this->belongsTo(User::class, 'entreprise_id');
    }

    public function membres()
    {
        return $this->hasMany(User::class, 'entreprise_id');
    }

    /**
     * Get all comments written by the user.
     */
    public function comments()
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * Get all comment likes by the user.
     */
    public function commentLikes()
    {
        return $this->hasMany(CommentLike::class);
    }

    /**
     * Get the user's avatar HTML
     */
    public function getAvatarHtml($size = 'md', $additionalClasses = '')
    {
        if ($this->avatar && $this->avatar_color) {
            $sizeClasses = [
                'xs' => 'avatar-xs',
                'sm' => 'avatar-sm',
                'md' => 'avatar-md',
                'lg' => 'avatar-lg',
                'xl' => 'avatar-xl'
            ];

            $sizeClass = $sizeClasses[$size] ?? $sizeClasses['md'];

            return sprintf(
                '<div class="avatar %s %s" style="background-color: %s; color: white;" title="%s">
                    <span class="avatar-initials">%s</span>
                </div>',
                $sizeClass,
                $additionalClasses,
                $this->avatar_color,
                $this->name,
                $this->avatar
            );
        }

        // Fallback si pas d'avatar
        return \App\Helpers\AvatarHelper::getAvatarHtml($this->name, $size, $additionalClasses);
    }

    // Méthodes avatar supprimées pour stabilité
}

