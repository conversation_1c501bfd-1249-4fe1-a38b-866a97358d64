@extends('theme')
@section('contenu')

<!-- début du titre de la page -->
<!-- fin du titre de la page -->

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header align-items-center d-flex">
                <h4 class="card-title mb-0 flex-grow-1">{{ __('message.Add Administrator') }}</h4>
                <div class="flex-shrink-0">
                </div>
            </div>
            <div class="card-body">
                <div class="live-preview">
                    <div class="row gy-4">
                        <form action="/ajoutadmin" method="POST">
                            @csrf

                            <div class="col-xxl-12 col-md-12">
                                <div>
                                    <label for="labelInput" class="form-label">{{ __('message.Name_admin') }}</label>
                                    <input type="text" class="form-control" id="labelInput" name="name">
                                </div>
                            </div>

                            <div class="col-xxl-12 col-md-12">
                                <div>
                                    <label for="iconInput" class="form-label">{{ __('message.Email') }}</label>
                                    <div class="form-icon">
                                        <input type="email" class="form-control form-control-icon" id="iconInput" placeholder="<EMAIL>" name="email">
                                        <i class="ri-mail-unread-line"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="col-xxl-12 col-md-12">
                                <div>
                                    <label for="exampleInputpassword" class="form-label">{{ __('message.Password') }}</label>
                                    <input type="password" class="form-control" id="exampleInputpassword" name="password">
                                </div>
                            </div>

                            <div>
                                <div class="col-xxl-12 col-md-12">
                                    <button type="submit" class="btn btn-primary">{{ __('message.Add_admin') }}</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="d-none code-view">
                    <pre class="language-markup" style="height: 450px;">
<code>&lt;!-- Champ de saisie de base --&gt;
                    </code></pre>
                </div>
            </div>
        </div>
    </div>
    <!-- fin de col -->
</div>
<!-- fin de row -->

@endsection
