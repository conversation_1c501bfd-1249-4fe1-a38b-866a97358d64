<?php

namespace App\Observers;

use App\Models\User;
use App\Helpers\AvatarHelper;

class UserObserver
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        // Générer automatiquement l'avatar lors de la création d'un utilisateur
        if (empty($user->avatar) || empty($user->avatar_color)) {
            AvatarHelper::updateUserAvatar($user);
        }
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        // Régénérer l'avatar si le nom a changé
        if ($user->isDirty('name') && !empty($user->name)) {
            AvatarHelper::updateUserAvatar($user);
        }
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        //
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     */
    public function forceDeleted(User $user): void
    {
        //
    }
}
