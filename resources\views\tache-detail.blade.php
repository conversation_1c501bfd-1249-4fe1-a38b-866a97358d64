@extends($userTheme ?? 'theme')
@section('contenu')
<div class="page-content">
    <div class="container-fluid">

        <!-- Page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
                    <h4 class="mb-sm-0">{{ __('message.Task Details') }}</h4>

                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="#">{{ __('message.Tasks_detail') }}</a></li>
                            <li class="breadcrumb-item active">{{ __('message.Task Details') }}</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tache Details -->
        <div class="row">
            <div class="col-xxl-3">
                <div class="card mb-3">
                    <div class="card-body">
                        <div class="mb-4">
                            <select class="form-control" disabled>
                                <option selected>{{ $tache->statut }}</option>
                            </select>
                        </div>
                        <div class="table-card">
                            <table class="table mb-0">
                                <tbody>
                                    <tr>
                                        <td class="fw-medium">{{ __('message.Tasks No') }}</td>
                                        <td>#{{ $tache->id }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-medium">{{ __('message.Tasks Title') }}</td>
                                        <td>{{ $tache->nom }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-medium">{{ __('message.Project Name') }}</td>
                                        <td>{{ $tache->projet->nom ?? 'N/A' }}</td>
                                    </tr>
                                    <tr>
                                        <td class="fw-medium">{{ __('message.Priority') }}</td>
                                        <td><span class="badge bg-danger-subtle text-danger">{{ __('message.High') }}</span></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-medium">{{ __('message.Status_detail') }}</td>
                                        <td><span class="badge bg-secondary-subtle text-secondary">{{ $tache->statut }}</span></td>
                                    </tr>
                                    <tr>
                                        <td class="fw-medium">{{ __('message.Due Date') }}</td>
                                        <td>{{ $tache->deadline }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xxl-9">
                <div class="card">
                    <div class="card-body">
                        <div class="text-muted">
                            <h6 class="mb-3 fw-semibold text-uppercase">{{ __('message.Description_detail') }}</h6>
                            <p>{{ $tache->description }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>


                            <div class="card">
                                <div class="card-header">
                                    <div>
                                        <ul class="nav nav-tabs-custom rounded card-header-tabs border-bottom-0" role="tablist">
                                            <li class="nav-item">
                                                <a class="nav-link active" data-bs-toggle="tab" href="#home-1" role="tab">
                                                    Commentaires ({{ $commentCount ?? 0 }})
                                                </a>
                                            </li>

                                        </ul>
                                        <!--end nav-->
                                    </div>
                                </div>
                                <div class="card-body">
                                    <div class="tab-content">
                                        <div class="tab-pane active" id="home-1" role="tabpanel">
                                            <h5 class="card-title mb-4">Commentaires</h5>
                                            <div data-simplebar style="height: 508px;" class="px-3 mx-n3 mb-2" id="comments-container">
                                                @if($tache->comments->count() > 0)
                                                    @foreach($tache->comments as $comment)
                                                        <div class="d-flex mb-4 comment-item" id="comment-{{ $comment->id }}">
                                                            <div class="flex-shrink-0">
                                                                <div class="avatar-xs rounded-circle bg-primary-subtle text-primary d-flex align-items-center justify-content-center">
                                                                    {{ strtoupper(substr($comment->user->name, 0, 1)) }}
                                                                </div>
                                                            </div>
                                                            <div class="flex-grow-1 ms-3">
                                                                <h5 class="fs-13">
                                                                    <span>{{ $comment->user->name }}</span>
                                                                    <small class="text-muted">{{ $comment->created_at->format('d M Y - H:i') }}</small>
                                                                </h5>
                                                                <p class="text-muted">{{ $comment->content }}</p>
                                                                <div class="d-flex align-items-center">
                                                                    @if(auth()->check() && in_array(auth()->user()->role, ['entreprise', 'membre', 'client']))
                                                                    <a href="javascript:void(0);" class="badge text-muted bg-light me-2 reply-btn"
                                                                       data-comment-id="{{ $comment->id }}">
                                                                        <i class="mdi mdi-reply"></i> Répondre
                                                                    </a>
                                                                    <form action="{{ route('comments.like', $comment->id) }}" method="POST" class="d-inline like-form">
                                                                        @csrf
                                                                        <button type="submit" class="badge text-muted bg-light border-0 like-btn">
                                                                            <i class="mdi mdi-heart{{ $comment->isLikedByUser(auth()->id()) ? '-fill text-danger' : '' }}"></i>
                                                                            <span class="like-count">{{ $comment->likes_count }}</span>
                                                                        </button>
                                                                    </form>
                                                                    @else
                                                                    <span class="badge text-muted bg-light">
                                                                        <i class="mdi mdi-heart"></i>
                                                                        <span>{{ $comment->likes_count }}</span>
                                                                    </span>
                                                                    @endif
                                                                </div>

                                                                <!-- Formulaire de réponse (caché par défaut) -->
                                                                <div class="reply-form mt-3" id="reply-form-{{ $comment->id }}" style="display: none;">
                                                                    <div class="row g-3">
                                                                        <div class="col-lg-12">
                                                                            <textarea class="form-control bg-light border-light reply-content" rows="2"
                                                                                      placeholder="Votre réponse..."></textarea>
                                                                        </div>
                                                                        <div class="col-12">
                                                                            <button type="button" class="btn btn-sm btn-primary submit-reply-btn" data-comment-id="{{ $comment->id }}" data-tache-id="{{ $tache->id }}">Envoyer</button>
                                                                            <button type="button" class="btn btn-sm btn-light cancel-reply">Annuler</button>
                                                                        </div>
                                                                    </div>
                                                                </div>

                                                                <!-- Réponses aux commentaires -->
                                                                @if($comment->replies->count() > 0)
                                                                    @foreach($comment->replies as $reply)
                                                                        <div class="d-flex mt-4 reply-item" id="comment-{{ $reply->id }}">
                                                                            <div class="flex-shrink-0">
                                                                                <div class="avatar-xs rounded-circle bg-info-subtle text-info d-flex align-items-center justify-content-center">
                                                                                    {{ strtoupper(substr($reply->user->name, 0, 1)) }}
                                                                                </div>
                                                                            </div>
                                                                            <div class="flex-grow-1 ms-3">
                                                                                <h5 class="fs-13">
                                                                                    <span>{{ $reply->user->name }}</span>
                                                                                    <small class="text-muted">{{ $reply->created_at->format('d M Y - H:i') }}</small>
                                                                                </h5>
                                                                                <p class="text-muted">{{ $reply->content }}</p>
                                                                                @if(auth()->check() && in_array(auth()->user()->role, ['entreprise', 'membre', 'client']))
                                                                                <form action="{{ route('comments.like', $reply->id) }}" method="POST" class="d-inline like-form">
                                                                                    @csrf
                                                                                    <button type="submit" class="badge text-muted bg-light border-0 like-btn">
                                                                                        <i class="mdi mdi-heart{{ $reply->isLikedByUser(auth()->id()) ? '-fill text-danger' : '' }}"></i>
                                                                                        <span class="like-count">{{ $reply->likes_count }}</span>
                                                                                    </button>
                                                                                </form>
                                                                                @else
                                                                                <span class="badge text-muted bg-light">
                                                                                    <i class="mdi mdi-heart"></i>
                                                                                    <span>{{ $reply->likes_count }}</span>
                                                                                </span>
                                                                                @endif
                                                                            </div>
                                                                        </div>
                                                                    @endforeach
                                                                @endif
                                                            </div>
                                                        </div>
                                                    @endforeach
                                                @else
                                                    <div class="text-center py-4">
                                                        <p class="text-muted">Aucun commentaire pour le moment.</p>
                                                    </div>
                                                @endif
                                            </div>

                                            @if(auth()->check() && in_array(auth()->user()->role, ['client', 'entreprise', 'membre']))
                                                <form id="comment-form" class="mt-4" action="{{ route('comments.store') }}" method="POST">
                                                    @csrf
                                                    <div class="row g-3">
                                                        <div class="col-lg-12">
                                                            <label for="comment-content" class="form-label">Ajouter un commentaire</label>
                                                            <textarea class="form-control bg-light border-light" id="comment-content" name="content"
                                                                      rows="3" placeholder="Votre commentaire..."></textarea>
                                                            <input type="hidden" name="tache_id" value="{{ $tache->id }}">
                                                            <input type="hidden" name="parent_id" value="">
                                                        </div>
                                                        <div class="col-12 text-end">
                                                            <button type="submit" class="btn btn-success">Publier</button>
                                                        </div>
                                                    </div>
                                                </form>
                                            @else
                                                <div class="alert alert-info mt-4">
                                                    <i class="ri-information-line me-2"></i>
                                                    Vous devez être connecté en tant que client, entreprise ou membre pour ajouter des commentaires.
                                                </div>
                                            @endif
                                        </div>
                                        <!--end tab-pane-->
                                        <div class="tab-pane" id="messages-1" role="tabpanel">
                                            <div class="table-responsive table-card">
                                                <table class="table table-borderless align-middle mb-0">
                                                    <thead class="table-light text-muted">
                                                        <tr>
                                                            <th scope="col">File Name</th>
                                                            <th scope="col">Type</th>
                                                            <th scope="col">Size</th>
                                                            <th scope="col">Upload Date</th>
                                                            <th scope="col">Action</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td>
                                                                <div class="d-flex align-items-center">
                                                                    <div class="avatar-sm">
                                                                        <div class="avatar-title bg-primary-subtle text-primary rounded fs-20">
                                                                            <i class="ri-file-zip-fill"></i>
                                                                        </div>
                                                                    </div>
                                                                    <div class="ms-3 flex-grow-1">
                                                                        <h6 class="fs-15 mb-0"><a href="javascript:void(0)">App pages.zip</a></h6>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>Zip File</td>
                                                            <td>2.22 MB</td>
                                                            <td>21 Dec, 2021</td>
                                                            <td>
                                                                <div class="dropdown">
                                                                    <a href="javascript:void(0);" class="btn btn-light btn-icon" id="dropdownMenuLink1" data-bs-toggle="dropdown" aria-expanded="true">
                                                                        <i class="ri-equalizer-fill"></i>
                                                                    </a>
                                                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink1" data-popper-placement="bottom-end" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate(0px, 23px);">
                                                                        <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-eye-fill me-2 align-middle text-muted"></i>View</a></li>
                                                                        <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-download-2-fill me-2 align-middle text-muted"></i>Download</a></li>
                                                                        <li class="dropdown-divider"></li>
                                                                        <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-delete-bin-5-line me-2 align-middle text-muted"></i>Delete</a></li>
                                                                    </ul>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <div class="d-flex align-items-center">
                                                                    <div class="avatar-sm">
                                                                        <div class="avatar-title bg-danger-subtle text-danger rounded fs-20">
                                                                            <i class="ri-file-pdf-fill"></i>
                                                                        </div>
                                                                    </div>
                                                                    <div class="ms-3 flex-grow-1">
                                                                        <h6 class="fs-15 mb-0"><a href="javascript:void(0);">Velzon admin.ppt</a></h6>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>PPT File</td>
                                                            <td>2.24 MB</td>
                                                            <td>25 Dec, 2021</td>
                                                            <td>
                                                                <div class="dropdown">
                                                                    <a href="javascript:void(0);" class="btn btn-light btn-icon" id="dropdownMenuLink2" data-bs-toggle="dropdown" aria-expanded="true">
                                                                        <i class="ri-equalizer-fill"></i>
                                                                    </a>
                                                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink2" data-popper-placement="bottom-end" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate(0px, 23px);">
                                                                        <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-eye-fill me-2 align-middle text-muted"></i>View</a></li>
                                                                        <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-download-2-fill me-2 align-middle text-muted"></i>Download</a></li>
                                                                        <li class="dropdown-divider"></li>
                                                                        <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-delete-bin-5-line me-2 align-middle text-muted"></i>Delete</a></li>
                                                                    </ul>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <div class="d-flex align-items-center">
                                                                    <div class="avatar-sm">
                                                                        <div class="avatar-title bg-info-subtle text-info rounded fs-20">
                                                                            <i class="ri-folder-line"></i>
                                                                        </div>
                                                                    </div>
                                                                    <div class="ms-3 flex-grow-1">
                                                                        <h6 class="fs-15 mb-0"><a href="javascript:void(0);">Images.zip</a></h6>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>ZIP File</td>
                                                            <td>1.02 MB</td>
                                                            <td>28 Dec, 2021</td>
                                                            <td>
                                                                <div class="dropdown">
                                                                    <a href="javascript:void(0);" class="btn btn-light btn-icon" id="dropdownMenuLink3" data-bs-toggle="dropdown" aria-expanded="true">
                                                                        <i class="ri-equalizer-fill"></i>
                                                                    </a>
                                                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink3" data-popper-placement="bottom-end" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate(0px, 23px);">
                                                                        <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-eye-fill me-2 align-middle"></i>View</a></li>
                                                                        <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-download-2-fill me-2 align-middle"></i>Download</a></li>
                                                                        <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-delete-bin-5-line me-2 align-middle"></i>Delete</a></li>
                                                                    </ul>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <div class="d-flex align-items-center">
                                                                    <div class="avatar-sm">
                                                                        <div class="avatar-title bg-danger-subtle text-danger rounded fs-20">
                                                                            <i class="ri-image-2-fill"></i>
                                                                        </div>
                                                                    </div>
                                                                    <div class="ms-3 flex-grow-1">
                                                                        <h6 class="fs-15 mb-0"><a href="javascript:void(0);">Bg-pattern.png</a></h6>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>PNG File</td>
                                                            <td>879 KB</td>
                                                            <td>02 Nov 2021</td>
                                                            <td>
                                                                <div class="dropdown">
                                                                    <a href="javascript:void(0);" class="btn btn-light btn-icon" id="dropdownMenuLink4" data-bs-toggle="dropdown" aria-expanded="true">
                                                                        <i class="ri-equalizer-fill"></i>
                                                                    </a>
                                                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuLink4" data-popper-placement="bottom-end" style="position: absolute; inset: 0px 0px auto auto; margin: 0px; transform: translate(0px, 23px);">
                                                                        <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-eye-fill me-2 align-middle"></i>View</a></li>
                                                                        <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-download-2-fill me-2 align-middle"></i>Download</a></li>
                                                                        <li><a class="dropdown-item" href="javascript:void(0);"><i class="ri-delete-bin-5-line me-2 align-middle"></i>Delete</a></li>
                                                                    </ul>
                                                                </div>
                                                            </td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                                <!--end table-->
                                            </div>
                                        </div>
                                        <!--end tab-pane-->
                                        <div class="tab-pane" id="profile-1" role="tabpanel">
                                            <h6 class="card-title mb-4 pb-2">Time Entries</h6>
                                            <div class="table-responsive table-card">
                                                <table class="table align-middle mb-0">
                                                    <thead class="table-light text-muted">
                                                        <tr>
                                                            <th scope="col">Member</th>
                                                            <th scope="col">Date</th>
                                                            <th scope="col">Duration</th>
                                                            <th scope="col">Timer Idle</th>
                                                            <th scope="col">Tasks Title</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <th scope="row">
                                                                <div class="d-flex align-items-center">
                                                                    <img src="assets/images/users/avatar-8.jpg" alt="" class="rounded-circle avatar-xxs">
                                                                    <div class="flex-grow-1 ms-2">
                                                                        <a href="pages-profile.html" class="fw-medium">Thomas Taylor</a>
                                                                    </div>
                                                                </div>
                                                            </th>
                                                            <td>02 Jan, 2022</td>
                                                            <td>3 hrs 12 min</td>
                                                            <td>05 min</td>
                                                            <td>Apps Pages</td>
                                                        </tr>
                                                        <tr>
                                                            <td>
                                                                <div class="d-flex align-items-center">
                                                                    <img src="assets/images/users/avatar-10.jpg" alt="" class="rounded-circle avatar-xxs">
                                                                    <div class="flex-grow-1 ms-2">
                                                                        <a href="pages-profile.html" class="fw-medium">Tonya Noble</a>
                                                                    </div>
                                                                </div>
                                                            </td>
                                                            <td>28 Dec, 2021</td>
                                                            <td>1 hrs 35 min</td>
                                                            <td>-</td>
                                                            <td>Profile Page Design</td>
                                                        </tr>
                                                        <tr>
                                                            <th scope="row">
                                                                <div class="d-flex align-items-center">
                                                                    <img src="assets/images/users/avatar-10.jpg" alt="" class="rounded-circle avatar-xxs">
                                                                    <div class="flex-grow-1 ms-2">
                                                                        <a href="pages-profile.html" class="fw-medium">Tonya Noble</a>
                                                                    </div>
                                                                </div>
                                                            </th>
                                                            <td>27 Dec, 2021</td>
                                                            <td>4 hrs 26 min</td>
                                                            <td>03 min</td>
                                                            <td>Ecommerce Dashboard</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                                <!--end table-->
                                            </div>
                                        </div>
                                        <!--edn tab-pane-->

                                    </div>
                                    <!--end tab-content-->
                                </div>
                            </div>
                            <!--end card-->
                        </div>
                        <!--end col-->
                    </div>
                    <!--end row-->

                    <div class="modal fade" id="inviteMembersModal" tabindex="-1" aria-labelledby="inviteMembersModalLabel" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content border-0">
                                <div class="modal-header p-3 ps-4 bg-success-subtle">
                                    <h5 class="modal-title" id="inviteMembersModalLabel">Team Members</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <div class="modal-body p-4">
                                    <div class="search-box mb-3">
                                        <input type="text" class="form-control bg-light border-light" placeholder="Search here...">
                                        <i class="ri-search-line search-icon"></i>
                                    </div>

                                    <div class="mb-4 d-flex align-items-center">
                                        <div class="me-2">
                                            <h5 class="mb-0 fs-13">Members :</h5>
                                        </div>
                                        <div class="avatar-group justify-content-center">
                                            <a href="javascript: void(0);" class="avatar-group-item" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-placement="top" title="Tonya Noble">
                                                <div class="avatar-xs">
                                                    <img src="assets/images/users/avatar-10.jpg" alt="" class="rounded-circle img-fluid">
                                                </div>
                                            </a>
                                            <a href="javascript: void(0);" class="avatar-group-item" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-placement="top" title="Thomas Taylor">
                                                <div class="avatar-xs">
                                                    <img src="assets/images/users/avatar-8.jpg" alt="" class="rounded-circle img-fluid">
                                                </div>
                                            </a>
                                            <a href="javascript: void(0);" class="avatar-group-item" data-bs-toggle="tooltip" data-bs-trigger="hover" data-bs-placement="top" title="Nancy Martino">
                                                <div class="avatar-xs">
                                                    <img src="assets/images/users/avatar-2.jpg" alt="" class="rounded-circle img-fluid">
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-light w-xs" data-bs-dismiss="modal">Cancel</button>
                                    <button type="button" class="btn btn-success w-xs">Assigned</button>
                                </div>
                            </div>
                            <!-- end modal-content -->
                        </div>
                        <!-- modal-dialog -->
                    </div>
                    <!-- end modal -->

                </div>
                <!-- container-fluid -->
            </div>


@endsection

@section('scripts')
<style>
    .like-btn {
        background: none;
        padding: 0.25rem 0.5rem;
        cursor: pointer;
        font-weight: normal;
        font-size: 0.75rem;
        line-height: 1;
        appearance: none;
    }
    .like-btn:hover {
        background-color: #f8f9fa;
    }
    .like-form {
        display: inline-block;
    }
</style>
<script>
    // Fonction pour afficher les erreurs dans la console
    function logError(error, context) {
        console.error(`Erreur dans ${context}:`, error);
        if (error.message) {
            console.error('Message:', error.message);
        }
        if (error.stack) {
            console.error('Stack:', error.stack);
        }
    }

    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM chargé, initialisation des gestionnaires d\'événements');

        // Vérifier le rôle de l'utilisateur
        const userRole = '{{ auth()->check() ? auth()->user()->role : "non connecté" }}';
        console.log('Rôle de l\'utilisateur:', userRole);
        // Gestion des commentaires
        const commentForm = document.getElementById('comment-form');
        if (commentForm) {
            commentForm.addEventListener('submit', function(e) {
                e.preventDefault();

                const content = document.getElementById('comment-content').value;

                if (!content.trim()) {
                    alert('Veuillez saisir un commentaire.');
                    return;
                }

                console.log('Envoi du commentaire principal');

                // Utiliser FormData pour envoyer les données du formulaire
                const formData = new FormData(this);

                // Envoi du commentaire via AJAX
                fetch('{{ route("comments.store") }}', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    console.log('Réponse commentaire reçue, status:', response.status);
                    if (!response.ok) {
                        throw new Error('Erreur réseau: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Réponse commentaire parsée:', data);

                    if (data.success) {
                        // Réinitialiser le formulaire
                        document.getElementById('comment-content').value = '';

                        // Recharger la page pour afficher le nouveau commentaire
                        window.location.reload();
                    } else {
                        alert(data.error || 'Une erreur est survenue lors de l\'ajout du commentaire.');
                    }
                })
                .catch(error => {
                    console.error('Erreur complète:', error);
                    alert('Une erreur est survenue: ' + error.message);
                });
            });
        }

        // Gestion des boutons de réponse
        document.querySelectorAll('.reply-btn').forEach(button => {
            button.addEventListener('click', function() {
                const commentId = this.getAttribute('data-comment-id');
                const replyForm = document.getElementById(`reply-form-${commentId}`);

                // Afficher/masquer le formulaire de réponse
                if (replyForm.style.display === 'none' || !replyForm.style.display) {
                    replyForm.style.display = 'block';
                } else {
                    replyForm.style.display = 'none';
                }
            });
        });

        // Gestion des boutons d'annulation de réponse
        document.querySelectorAll('.cancel-reply').forEach(button => {
            button.addEventListener('click', function() {
                const form = this.closest('.reply-form');
                form.style.display = 'none';
            });
        });

        // Gestion des boutons de soumission de réponse
        document.querySelectorAll('.submit-reply-btn').forEach(button => {
            button.addEventListener('click', function() {
                const commentId = this.getAttribute('data-comment-id');
                const tacheId = this.getAttribute('data-tache-id');
                const replyForm = document.getElementById(`reply-form-${commentId}`);
                const content = replyForm.querySelector('.reply-content').value;

                console.log('Bouton de réponse cliqué pour le commentaire ID:', commentId);
                console.log('Contenu de la réponse:', content);

                if (!content.trim()) {
                    alert('Veuillez saisir une réponse.');
                    return;
                }

                // Créer un FormData pour l'envoi
                const formData = new FormData();
                formData.append('_token', '{{ csrf_token() }}');
                formData.append('content', content);
                formData.append('tache_id', tacheId);
                formData.append('parent_id', commentId);

                fetch('{{ route("comments.store") }}', {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    console.log('Réponse reçue pour réponse, status:', response.status);
                    if (!response.ok) {
                        throw new Error('Erreur réseau: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Réponse reply parsée:', data);

                    if (data.success) {
                        // Réinitialiser le formulaire et le masquer
                        replyForm.querySelector('.reply-content').value = '';
                        replyForm.style.display = 'none';

                        // Recharger la page pour afficher la nouvelle réponse
                        alert('Réponse ajoutée avec succès!');
                        window.location.reload();
                    } else {
                        alert(data.error || 'Une erreur est survenue lors de l\'ajout de la réponse.');
                    }
                })
                .catch(error => {
                    console.error('Erreur complète:', error);
                    alert('Une erreur est survenue: ' + error.message);
                });
            });
        });

        // Gestion des formulaires de like avec AJAX
        document.querySelectorAll('.like-form').forEach(form => {
            form.addEventListener('submit', function(e) {
                e.preventDefault();
                console.log('Formulaire de like soumis');

                const formAction = this.getAttribute('action');
                const formData = new FormData(this);
                const likeBtn = this.querySelector('.like-btn');
                const likeCount = likeBtn.querySelector('.like-count');
                const icon = likeBtn.querySelector('i');

                fetch(formAction, {
                    method: 'POST',
                    body: formData
                })
                .then(response => {
                    console.log('Réponse like reçue, status:', response.status);
                    if (!response.ok) {
                        throw new Error('Erreur réseau: ' + response.status);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('Réponse like parsée:', data);

                    if (data.success) {
                        likeCount.textContent = data.likes_count;

                        if (data.action === 'liked') {
                            icon.classList.remove('mdi-heart');
                            icon.classList.add('mdi-heart-fill', 'text-danger');
                        } else {
                            icon.classList.remove('mdi-heart-fill', 'text-danger');
                            icon.classList.add('mdi-heart');
                        }
                    } else {
                        alert(data.error || 'Une erreur est survenue.');
                    }
                })
                .catch(error => {
                    console.error('Erreur complète:', error);
                    alert('Une erreur est survenue: ' + error.message);
                });
            });
        });

        // Supprimer le rafraîchissement automatique
        console.log('Initialisation terminée');
    });
</script>
@endsection