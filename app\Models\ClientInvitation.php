<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ClientInvitation extends Model
{
    use HasFactory;

    protected $fillable = [
        'name',
        'email',
        'password',
        'token',
        'invited_by',
        'expires_at'
    ];

    protected $casts = [
        'expires_at' => 'datetime',
    ];

    /**
     * Relation avec l'utilisateur qui a envoyé l'invitation
     */
    public function inviter()
    {
        return $this->belongsTo(User::class, 'invited_by');
    }

    /**
     * Vérifie si l'invitation a expiré
     */
    public function isExpired()
    {
        return $this->expires_at->isPast();
    }
}
