<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Tache extends Model
{
    use HasFactory;

    protected $table = 'taches';

    protected $fillable = [
        'nom',
        'description',
        'deadline',
        'statut',
        'avancement',
        'projet_id',
        'personnel_id', // Ajouter personnel_id pour l'assignation
    ];

    /**
     * Relation : Une tâche appartient à un projet.
     */
    public function projet()
    {
        return $this->belongsTo(Projet::class);
    }

    /**
     * Relation : Une tâche peut être assignée à un personnel (facultatif).
     */
    public function personnel()
    {
        return $this->belongsTo(User::class, 'personnel_id');
    }

    /**
     * Relation : Une tâche peut avoir plusieurs commentaires.
     */
    public function comments()
    {
        return $this->hasMany(Comment::class, 'tache_id')->whereNull('parent_id')->orderBy('created_at', 'desc');
    }

    /**
     * Relation : Récupérer tous les commentaires (y compris les réponses) pour une tâche.
     */
    public function allComments()
    {
        return $this->hasMany(Comment::class, 'tache_id')->orderBy('created_at', 'desc');
    }
}
