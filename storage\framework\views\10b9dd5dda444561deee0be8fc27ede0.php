
<?php $__env->startSection('contenu'); ?>

<div class="container-fluid">

    <!-- début du titre de la page -->
    <!-- fin du titre de la page -->

    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1"><?php echo e(__('message.Admin List')); ?></h4>
                    <div class="flex-shrink-0">
                    </div>
                </div><!-- fin de l'en-tête de la carte -->

                <div class="card-body">

                    <div class="live-preview">
                        <div class="table-responsive">
                            <?php if(session('message')): ?>
                            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                <?php echo e(session('message')); ?>

                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
                            </div>
                            <?php endif; ?>

                            <table class="table align-middle table-nowrap mb-0">
                                <thead>
                                    <tr>
                                        <th scope="col"><?php echo e(__('message.ID_admin')); ?></th>
                                        <th scope="col"><?php echo e(__('message.Name_admin_list')); ?></th>
                                        <th scope="col"><?php echo e(__('message.Email_admin')); ?></th>
                                        <th scope="col"><?php echo e(__('message.Password_admin')); ?></th>
                                        <th scope="col"><?php echo e(__('message.Action_admin')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <th scope="row"><a href="#" class="fw-medium">#<?php echo e($item->id); ?></a></th>
                                        <td><?php echo e($item->name); ?></td>
                                        <td><?php echo e($item->email); ?></td>
                                        <td>************</td>
                                        <td>
                                            <a href="/modifier-admin/<?php echo e($item->id); ?>" type="button" class="btn btn-primary">
                                                <?php echo e(__('message.Edit_admin')); ?> <i class="bx bx-edit-alt"></i>
                                            </a>
                                            <a href="/suppAdmin/<?php echo e($item->id); ?>" class="btn btn-danger" onclick="return confirm('<?php echo e(__('message.Confirm delete admin')); ?>')">
                                                <?php echo e(__('message.Delete_admin')); ?> <i class="bx bx-trash"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div><!-- fin du corps de la carte -->
            </div><!-- fin de la carte -->
        </div>
        <!-- fin col -->
    </div>
    <!-- fin ligne -->

</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($userTheme ?? 'theme', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\task\resources\views/liste-admin.blade.php ENDPATH**/ ?>