<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Projet;
use App\Models\Tache;
use App\Models\Contact;
use Illuminate\Support\Facades\Auth;

class SearchController extends Controller
{
    /**
     * Recherche globale dans l'application
     */
    public function search(Request $request)
    {
        $query = $request->input('query');
        $results = [];
        $count = 0;

        if (!$query) {
            return view('search-results', [
                'results' => $results,
                'count' => $count,
                'query' => $query
            ]);
        }

        // Recherche dans les utilisateurs (admins et personnel)
        $users = User::where('name', 'LIKE', "%{$query}%")
            ->orWhere('email', 'LIKE', "%{$query}%")
            ->get();
        
        // Recherche dans les projets
        $projets = Projet::where('project_name', 'LIKE', "%{$query}%")
            ->orWhere('task_title', 'LIKE', "%{$query}%")
            ->orWhere('task_description', 'LIKE', "%{$query}%")
            ->get();
        
        // Recherche dans les tâches
        $taches = Tache::where('nom', 'LIKE', "%{$query}%")
            ->orWhere('description', 'LIKE', "%{$query}%")
            ->get();
        
        // Recherche dans les contacts
        $contacts = Contact::where('name', 'LIKE', "%{$query}%")
            ->orWhere('email', 'LIKE', "%{$query}%")
            ->orWhere('sujet', 'LIKE', "%{$query}%")
            ->orWhere('message', 'LIKE', "%{$query}%")
            ->get();

        // Combiner les résultats
        $results = [
            'users' => $users,
            'projets' => $projets,
            'taches' => $taches,
            'contacts' => $contacts
        ];

        // Calculer le nombre total de résultats
        $count = $users->count() + $projets->count() + $taches->count() + $contacts->count();

        return view('search-results', [
            'results' => $results,
            'count' => $count,
            'query' => $query
        ]);
    }

    /**
     * API pour la recherche dynamique
     */
    public function searchApi(Request $request)
    {
        $query = $request->input('query');
        $results = [];

        if (!$query || strlen($query) < 2) {
            return response()->json([
                'results' => $results,
                'count' => 0
            ]);
        }

        // Limiter le nombre de résultats pour chaque catégorie
        $limit = 5;

        // Recherche dans les utilisateurs (admins et personnel)
        $users = User::where('name', 'LIKE', "%{$query}%")
            ->orWhere('email', 'LIKE', "%{$query}%")
            ->limit($limit)
            ->get()
            ->map(function($user) {
                return [
                    'id' => $user->id,
                    'name' => $user->name,
                    'email' => $user->email,
                    'role' => $user->role,
                    'type' => 'user',
                    'url' => $user->role === 'admin' 
                        ? '/modifier-admin/' . $user->id 
                        : '/profil'
                ];
            });
        
        // Recherche dans les projets
        $projets = Projet::where('project_name', 'LIKE', "%{$query}%")
            ->orWhere('task_title', 'LIKE', "%{$query}%")
            ->limit($limit)
            ->get()
            ->map(function($projet) {
                return [
                    'id' => $projet->id,
                    'name' => $projet->project_name,
                    'description' => $projet->task_title,
                    'type' => 'projet',
                    'url' => '/projet/edit/' . $projet->id
                ];
            });
        
        // Recherche dans les tâches
        $taches = Tache::where('nom', 'LIKE', "%{$query}%")
            ->limit($limit)
            ->get()
            ->map(function($tache) {
                return [
                    'id' => $tache->id,
                    'name' => $tache->nom,
                    'description' => substr($tache->description, 0, 50) . (strlen($tache->description) > 50 ? '...' : ''),
                    'type' => 'tache',
                    'url' => '/tache-detail/' . $tache->id
                ];
            });
        
        // Recherche dans les contacts
        $contacts = Contact::where('name', 'LIKE', "%{$query}%")
            ->orWhere('sujet', 'LIKE', "%{$query}%")
            ->limit($limit)
            ->get()
            ->map(function($contact) {
                return [
                    'id' => $contact->id,
                    'name' => $contact->name,
                    'description' => $contact->sujet,
                    'type' => 'contact',
                    'url' => '/afficher-message/' . $contact->id
                ];
            });

        // Combiner les résultats
        $results = [
            'users' => $users,
            'projets' => $projets,
            'taches' => $taches,
            'contacts' => $contacts
        ];

        // Calculer le nombre total de résultats
        $count = $users->count() + $projets->count() + $taches->count() + $contacts->count();

        return response()->json([
            'results' => $results,
            'count' => $count
        ]);
    }
}
