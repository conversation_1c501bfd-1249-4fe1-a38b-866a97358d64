
<?php $__env->startSection('contenu'); ?>
<div class="container-fluid">

    <!-- Début titre de la page -->
    <!-- Fin titre de la page -->

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1"><?php echo e(__('message.edit_project')); ?></h4>
                    <div class="flex-shrink-0">
                    </div>
                </div><!-- fin entête de carte -->

                <div class="card-body">
                    <div class="live-preview">
                        <div class="row gy-4">
                            <form action="/modifier-projet" method="POST" enctype="multipart/form-data">
                                <?php echo csrf_field(); ?>
                                <input type="hidden" name="id" value="<?php echo e($project->id); ?>">

                                <div class="col-xxl-12 col-md-12">
                                    <div>
                                        <label for="basiInput" class="form-label"><?php echo e(__('message.project_name')); ?></label>
                                        <input type="text" placeholder="<?php echo e(__('message.project_name')); ?>" class="form-control" name="project_name" value="<?php echo e($project->project_name); ?>" id="basiInput">
                                    </div>
                                </div>

                                <div class="col-xxl-12 col-md-12">
                                    <div>
                                        <label for="descriptionInput" class="form-label"><?php echo e(__('message.project_description')); ?></label>
                                        <textarea placeholder="<?php echo e(__('message.project_description')); ?>" class="form-control" name="project_description" id="descriptionInput" rows="3"><?php echo e($project->project_description); ?></textarea>
                                    </div>
                                </div>

                                <div class="col-xxl-12 col-md-12">
                                    <div>
                                        <label for="startDateInput" class="form-label"><?php echo e(__('message.start_date')); ?></label>
                                        <input type="date" class="form-control" name="start_date" value="<?php echo e($project->start_date); ?>" id="startDateInput">
                                    </div>
                                </div>

                                <div class="col-xxl-12 col-md-12">
                                    <div>
                                        <label for="endDateInput" class="form-label"><?php echo e(__('message.end_date')); ?></label>
                                        <input type="date" class="form-control" name="end_date" value="<?php echo e($project->end_date); ?>" id="endDateInput">
                                    </div>
                                </div>

                                <div class="col-xxl-12 col-md-12">
                                    <div>
                                        <label for="task_image" class="form-label"><?php echo e(__('message.image')); ?> (<?php echo e(__('message.optional')); ?>)</label>
                                        <input type="file" class="form-control" id="task_image" name="task_image" accept="image/*">
                                        <?php if($project->task_image): ?>
                                            <div class="mt-2">
                                                <small class="text-muted"><?php echo e(__('message.Current image')); ?> :</small><br>
                                                <img src="<?php echo e(asset("storage/{$project->task_image}")); ?>" alt="<?php echo e(__('message.Project image')); ?>" style="max-width: 100px; max-height: 100px;">
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>

                                <div class="col-xxl-12 col-md-12">
                                    <div>
                                        <label class="form-label"><?php echo e(__('message.Add Team Members')); ?></label>
                                        <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                            <?php
                                                $selectedMembers = $project->members ? json_decode($project->members, true) : [];
                                            ?>
                                            <?php if(isset($membres) && count($membres) > 0): ?>
                                                <?php $__currentLoopData = $membres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $membre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" name="members[]" value="<?php echo e($membre->id); ?>" id="membre-<?php echo e($membre->id); ?>"
                                                           <?php if(in_array($membre->id, $selectedMembers)): ?> checked <?php endif; ?>>
                                                    <label class="form-check-label d-flex align-items-center" for="membre-<?php echo e($membre->id); ?>">
                                                        <div class="avatar avatar-xs me-2" style="background-color: #4ECDC4; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px;">
                                                            <?php echo e(strtoupper(substr($membre->name, 0, 1))); ?>

                                                        </div>
                                                        <span><?php echo e($membre->name); ?> - <?php echo e($membre->poste ?? 'N/A'); ?></span>
                                                    </label>
                                                </div>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <?php else: ?>
                                                <p class="text-muted mb-0"><?php echo e(__('message.No members available for this company')); ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>

                                <div class="col-xxl-12 col-md-12">
                                    <button type="submit" class="btn btn-primary"><?php echo e(__('message.edit')); ?></button>
                                </div>
                            </form>
                        </div>

                        <div class="d-none code-view">
                            <pre class="language-markup" style="height: 450px;"><code>&lt;!-- Formulaire de modification projet --&gt;
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- fin col -->
    </div>
    <!-- fin ligne -->

</div> <!-- container-fluid -->
<?php $__env->stopSection(); ?>

<?php echo $__env->make($userTheme ?? 'theme', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\task\resources\views/modifier-projet.blade.php ENDPATH**/ ?>