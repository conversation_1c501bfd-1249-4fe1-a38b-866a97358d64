@extends($userTheme ?? 'theme')

@section('contenu')

<div class="container-fluid">
    @if(session('message'))
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            {{ session('message') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
        </div>
    @endif

    @if ($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif

    <div class="card">
        <div class="card-header">
            <h4 class="card-title mb-0">Modifier un Personnel</h4>
        </div>
        <div class="card-body">
            <form action="/modifier-personnel" method="POST">
                @csrf
                <input type="hidden" name="id" value="{{ $data->id }}">

                <div class="mb-3">
                    <label>Nom</label>
                    <input type="text" class="form-control" name="nom" value="{{ old('nom', $data->name) }}">
                </div>

                <div class="mb-3">
                    <label>Email</label>
                    <input type="email" class="form-control" name="email" value="{{ old('email', $data->email) }}">
                </div>

                <div class="mb-3">
                    <label>Mot de passe (laisser vide pour ne pas changer)</label>
                    <input type="password" class="form-control" name="password">
                </div>

                <div class="mb-3">
                    <label>Confirmer le mot de passe</label>
                    <input type="password" class="form-control" name="password_confirmation">
                </div>

                <div class="mb-3">
                    <label>Poste</label>
                    <select name="poste" class="form-select" required>
                        <option value="">Sélectionner un poste</option>
                        <option value="Développeur Frontend" {{ old('poste', $data->poste) == 'Développeur Frontend' ? 'selected' : '' }}>Développeur Frontend</option>
                        <option value="Développeur Backend" {{ old('poste', $data->poste) == 'Développeur Backend' ? 'selected' : '' }}>Développeur Backend</option>
                        <option value="Chef de projet" {{ old('poste', $data->poste) == 'Chef de projet' ? 'selected' : '' }}>Chef de projet</option>
                        <option value="Concepteur" {{ old('poste', $data->poste) == 'Concepteur' ? 'selected' : '' }}>Concepteur</option>
                        <option value="Testeur" {{ old('poste', $data->poste) == 'Testeur' ? 'selected' : '' }}>Testeur</option>
                        <option value="Designer UX/UI" {{ old('poste', $data->poste) == 'Designer UX/UI' ? 'selected' : '' }}>Designer UX/UI</option>
                    </select>
                </div>

                <button type="submit" class="btn btn-primary">Modifier</button>
            </form>
        </div>
    </div>
</div>

@endsection
