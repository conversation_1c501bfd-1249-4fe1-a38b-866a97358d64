@extends($userTheme ?? 'theme')

@section('contenu')
<div class="page-content">
    <div class="container-fluid">
        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
                    <h4 class="mb-sm-0">
                        <i class="ri-notification-line text-primary me-2"></i>{{ __('message.Notifications') }}
                    </h4>
                    <div class="page-title-right">
                        <ol class="breadcrumb m-0">
                            <li class="breadcrumb-item"><a href="javascript: void(0);">{{ __('message.TaskFlow') }}</a></li>
                            <li class="breadcrumb-item active">{{ __('message.Notifications') }}</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <div class="row">
            <div class="col-lg-12">
                <div class="card">
                    <div class="card-header d-flex align-items-center">
                        <h5 class="card-title mb-0 flex-grow-1">{{ __('message.Notifications List') }}</h5>
                        <div class="flex-shrink-0">
                            <button id="mark-all-read" class="btn btn-soft-primary">
                                <i class="ri-checkbox-multiple-line align-bottom me-1"></i> {{ __('message.Mark All as Read') }}
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover table-striped align-middle mb-0">
                                <thead class="table-light text-muted">
                                    <tr>
                                        <th scope="col">{{ __('message.Type') }}</th>
                                        <th scope="col">{{ __('message.Message') }}</th>
                                        <th scope="col">{{ __('message.Date') }}</th>
                                        <th scope="col">{{ __('message.Status') }}</th>
                                        <th scope="col">{{ __('message.Actions') }}</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @forelse($notifications as $notification)
                                        <tr class="{{ $notification->read ? '' : 'table-light' }}">
                                            <td>
                                                @if($notification->type == 'tache_created')
                                                    <span class="badge bg-success-subtle text-success">
                                                        <i class="ri-task-line me-1"></i> {{ __('message.Task Created') }}
                                                    </span>
                                                @elseif($notification->type == 'tache_updated')
                                                    <span class="badge bg-info-subtle text-info">
                                                        <i class="ri-edit-box-line me-1"></i> {{ __('message.Task Updated') }}
                                                    </span>
                                                @elseif($notification->type == 'tache_assigned')
                                                    <span class="badge bg-primary-subtle text-primary">
                                                        <i class="ri-user-follow-line me-1"></i> {{ __('message.Task Assigned') }}
                                                    </span>
                                                @else
                                                    <span class="badge bg-secondary-subtle text-secondary">
                                                        <i class="ri-notification-line me-1"></i> {{ __('message.Other') }}
                                                    </span>
                                                @endif
                                            </td>
                                            <td>{{ $notification->message }}</td>
                                            <td>{{ $notification->created_at->format('d/m/Y H:i') }}</td>
                                            <td>
                                                @if($notification->read)
                                                    <span class="badge bg-success-subtle text-success">{{ __('message.Read') }}</span>
                                                @else
                                                    <span class="badge bg-warning-subtle text-warning">{{ __('message.Unread') }}</span>
                                                @endif
                                            </td>
                                            <td>
                                                @if($notification->tache_id)
                                                    <a href="{{ route('tache-detail', $notification->tache_id) }}" class="btn btn-sm btn-primary mark-as-read" data-id="{{ $notification->id }}">
                                                        <i class="ri-eye-line"></i> {{ __('message.View Task') }}
                                                    </a>
                                                @endif
                                                @if(!$notification->read)
                                                    <button class="btn btn-sm btn-soft-success mark-as-read-btn" data-id="{{ $notification->id }}">
                                                        <i class="ri-check-double-line"></i> {{ __('message.Mark as Read') }}
                                                    </button>
                                                @endif
                                            </td>
                                        </tr>
                                    @empty
                                        <tr>
                                            <td colspan="5" class="text-center py-4">
                                                <div class="avatar-md mx-auto mb-3">
                                                    <div class="avatar-title bg-light text-secondary rounded-circle fs-24">
                                                        <i class="ri-notification-off-line"></i>
                                                    </div>
                                                </div>
                                                <h5>{{ __('message.No Notifications') }}</h5>
                                            </td>
                                        </tr>
                                    @endforelse
                                </tbody>
                            </table>
                        </div>

                        <div class="d-flex justify-content-end mt-3">
                            {{ $notifications->links() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Marquer une notification comme lue
        const markAsReadButtons = document.querySelectorAll('.mark-as-read-btn');
        markAsReadButtons.forEach(button => {
            button.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                markAsRead(id, this);
            });
        });

        // Marquer toutes les notifications comme lues
        const markAllReadButton = document.getElementById('mark-all-read');
        if (markAllReadButton) {
            markAllReadButton.addEventListener('click', function() {
                markAllAsRead();
            });
        }

        // Marquer comme lu lors du clic sur le lien de la tâche
        const taskLinks = document.querySelectorAll('.mark-as-read');
        taskLinks.forEach(link => {
            link.addEventListener('click', function() {
                const id = this.getAttribute('data-id');
                markAsRead(id);
            });
        });

        // Fonction pour marquer une notification comme lue
        function markAsRead(id, button = null) {
            fetch(`/notifications/${id}/read`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success && button) {
                    // Mettre à jour l'interface utilisateur
                    const row = button.closest('tr');
                    row.classList.remove('table-light');

                    const statusCell = row.querySelector('td:nth-child(4)');
                    statusCell.innerHTML = '<span class="badge bg-success-subtle text-success">{{ __('message.Read') }}</span>';

                    button.remove();

                    // Mettre à jour le compteur de notifications
                    updateNotificationCount();
                }
            })
            .catch(error => console.error('Error:', error));
        }

        // Fonction pour marquer toutes les notifications comme lues
        function markAllAsRead() {
            fetch('/notifications/read-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Recharger la page pour mettre à jour l'interface utilisateur
                    window.location.reload();
                }
            })
            .catch(error => console.error('Error:', error));
        }

        // Fonction pour mettre à jour le compteur de notifications
        function updateNotificationCount() {
            fetch('/notifications/count')
            .then(response => response.json())
            .then(data => {
                const badge = document.querySelector('#userNotifContainer');
                if (badge) {
                    badge.textContent = data.count;
                    if (data.count === 0) {
                        badge.style.display = 'none';
                    } else {
                        badge.style.display = 'inline-block';
                    }
                }
            })
            .catch(error => console.error('Error:', error));
        }
    });
</script>
@endpush
@endsection
