<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Helpers\AvatarHelper;

class EnsureUserHasAvatar
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Vérifier si l'utilisateur est connecté
        if ($request->user()) {
            $user = $request->user();

            // Générer l'avatar si l'utilisateur n'en a pas
            if (empty($user->avatar) || empty($user->avatar_color)) {
                AvatarHelper::updateUserAvatar($user);
            }
        }

        return $next($request);
    }
}
