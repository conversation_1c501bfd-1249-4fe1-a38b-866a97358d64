<?php

use App\Helpers\ThemeHelper;
use App\Helpers\AvatarHelper;

if (!function_exists('user_theme')) {
    /**
     * Obtenir le thème de l'utilisateur actuel
     *
     * @return string
     */
    function user_theme()
    {
        return ThemeHelper::getUserTheme();
    }
}

if (!function_exists('avatar')) {
    /**
     * Générer un avatar HTML à partir d'un nom
     *
     * @param string $name
     * @param string $size
     * @param string $classes
     * @return string
     */
    function avatar($name, $size = 'md', $classes = '')
    {
        return AvatarHelper::getAvatarHtml($name, $size, $classes);
    }
}

if (!function_exists('user_avatar')) {
    /**
     * Générer un avatar HTML pour un utilisateur
     *
     * @param \App\Models\User|null $user
     * @param string $size
     * @param string $classes
     * @return string
     */
    function user_avatar($user, $size = 'md', $classes = '')
    {
        if (!$user || !method_exists($user, 'getAvatarHtml')) {
            return '';
        }

        return $user->getAvatarHtml($size, $classes);
    }
}

if (!function_exists('avatar_initials')) {
    /**
     * Obtenir les initiales d'un nom
     *
     * @param string $name
     * @return string
     */
    function avatar_initials($name)
    {
        return AvatarHelper::getInitials($name);
    }
}

if (!function_exists('avatar_color')) {
    /**
     * Obtenir une couleur d'avatar basée sur un nom
     *
     * @param string $name
     * @return string
     */
    function avatar_color($name)
    {
        return AvatarHelper::getColorFromName($name);
    }
}

if (!function_exists('project_image')) {
    /**
     * Obtenir l'URL d'une image de projet
     *
     * @param string|null $path
     * @param string $size
     * @return string
     */
    function project_image($path, $size = 'original')
    {
        return \App\Helpers\ImageHelper::getImageUrl(
            $path,
            $size,
            asset('assets/images/defaults/project-placeholder.svg')
        );
    }
}

if (!function_exists('upload_image')) {
    /**
     * Upload une image
     *
     * @param \Illuminate\Http\UploadedFile $file
     * @param string $folder
     * @param array $sizes
     * @return array
     */
    function upload_image($file, $folder = 'projects', $sizes = ['original', 'thumbnail'])
    {
        return \App\Helpers\ImageHelper::uploadImage($file, $folder, $sizes);
    }
}
