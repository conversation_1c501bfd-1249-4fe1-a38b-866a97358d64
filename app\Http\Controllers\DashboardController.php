<?php

namespace App\Http\Controllers;

use App\Models\entreprise;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\projet;

class DashboardController extends Controller
{
    public function index()
    {
        // Compteurs par rôle d'utilisateur
        $adminCount = User::where('role', 'admin')->count();
        $entrepriseCount = User::where('role', 'entreprise')->count();
        $membreCount = User::where('role', 'membre')->count();
        $clientCount = User::where('role', 'client')->count();

        // Nombre total de projets
        $projectCount = projet::count();

        // Nombre total d'entreprises (table entreprise)
        $entreCount = entreprise::count();

        // Récupérer les 5 projets avec le plus de tâches
        $topProjects = Projet::withCount(['taches as total_tasks',
                                         'taches as completed_tasks' => function ($query) {
                                             $query->where('statut', 'done');
                                         },
                                         'taches as in_progress_tasks' => function ($query) {
                                             $query->where('statut', 'doing');
                                         },
                                         'taches as pending_tasks' => function ($query) {
                                             $query->whereIn('statut', ['to_do', 'bug']);
                                         }])
                            ->orderBy('total_tasks', 'desc')
                            ->take(5)
                            ->get();

        // Préparer les données pour l'histogramme
        $projectNames = $topProjects->pluck('project_name')->toArray();
        $completedTasks = $topProjects->pluck('completed_tasks')->toArray();
        $inProgressTasks = $topProjects->pluck('in_progress_tasks')->toArray();
        $pendingTasks = $topProjects->pluck('pending_tasks')->toArray();

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        // Passer les variables à la vue avec le thème approprié
        return view('dashboard', compact(
            'adminCount',
            'entrepriseCount',
            'membreCount',
            'clientCount',
            'projectCount',
            'entreCount',
            'projectNames',
            'completedTasks',
            'inProgressTasks',
            'pendingTasks'
        ))->with('userTheme', $theme);
    }
}

