<svg width="400" height="300" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  <rect width="100%" height="100%" fill="url(#grad1)" opacity="0.1"/>
  <rect x="50" y="50" width="300" height="200" fill="none" stroke="#667eea" stroke-width="2" stroke-dasharray="10,5" rx="10"/>
  <circle cx="200" cy="120" r="30" fill="#667eea" opacity="0.3"/>
  <text x="200" y="130" font-family="Arial, sans-serif" font-size="16" fill="#667eea" text-anchor="middle" font-weight="bold">PROJET</text>
  <text x="200" y="200" font-family="Arial, sans-serif" font-size="12" fill="#6c757d" text-anchor="middle">Image non disponible</text>
</svg>
