@extends('layouts.shared')

@section('shared_content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
                <h4 class="mb-sm-0">Vue partagée d'exemple</h4>
                <div class="page-title-right">
                    <ol class="breadcrumb m-0">
                        <li class="breadcrumb-item"><a href="javascript: void(0);">TaskFlow</a></li>
                        <li class="breadcrumb-item active">Vue partagée</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title">Vue partagée avec thème dynamique</h4>
                </div>
                <div class="card-body">
                    <p>Cette vue utilise le thème <strong>{{ $userTheme }}</strong> qui est déterminé en fonction du rôle de l'utilisateur connecté.</p>
                    
                    <div class="alert alert-info">
                        <p>Le système de thèmes permet d'afficher la même vue avec différents thèmes en fonction du rôle de l'utilisateur :</p>
                        <ul>
                            <li><strong>Admin</strong> : utilise le thème <code>theme</code></li>
                            <li><strong>Entreprise</strong> : utilise le thème <code>admintheme</code></li>
                            <li><strong>Client</strong> : utilise le thème <code>clientheme</code></li>
                            <li><strong>Membre</strong> : utilise le thème <code>persotheme</code></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
