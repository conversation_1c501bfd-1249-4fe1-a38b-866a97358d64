<?php

namespace App\Listeners;

use App\Events\TaskUpdated;
use App\Models\Notification;
use App\Models\Projet;
use App\Models\User;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\App;

class UpdateTaskNotification
{
    /**
     * Create the event listener.
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle(TaskUpdated $event): void
    {
        // Récupérer la tâche et l'utilisateur qui l'a mise à jour
        $tache = $event->tache;
        $user = $event->user;

        // Récupérer le projet associé à la tâche
        $projet = Projet::find($tache->projet_id);

        // Récupérer l'entreprise propriétaire du projet
        $entreprise = User::find($projet->user_id);

        // Créer une notification pour l'entreprise
        Notification::create([
            'type' => 'tache_updated',
            'user_id' => $entreprise->id,
            'created_by' => $user->id,
            'tache_id' => $tache->id,
            'message' => __('message.task_updated', [
                'task' => $tache->nom,
                'project' => $projet->project_name
            ])
        ]);

        // Si la tâche est assignée à un membre du personnel, créer une notification pour lui aussi
        if ($tache->personnel_id && $tache->personnel_id != $user->id) {
            Notification::create([
                'type' => 'tache_updated',
                'user_id' => $tache->personnel_id,
                'created_by' => $user->id,
                'tache_id' => $tache->id,
                'message' => __('message.assigned_task_updated', [
                    'task' => $tache->nom
                ])
            ]);
        }
    }
}
