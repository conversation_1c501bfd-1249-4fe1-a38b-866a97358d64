<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Comment;
use App\Models\CommentLike;
use Illuminate\Support\Facades\Auth;

class CommentController extends Controller
{
    /**
     * Ajouter un nouveau commentaire
     */
    public function store(Request $request)
    {
        // Log pour déboguer
        \Log::info('Requête reçue dans CommentController@store', [
            'content_type' => $request->header('Content-Type'),
            'user' => Auth::check() ? Auth::user()->id : 'non connecté',
            'role' => Auth::check() ? Auth::user()->role : 'aucun',
            'method' => $request->method(),
            'all_headers' => $request->headers->all(),
            'all_data' => $request->all(),
            'is_json' => $request->isJson(),
            'ajax' => $request->ajax(),
            'wants_json' => $request->wantsJson()
        ]);

        $request->validate([
            'content' => 'required|string',
            'tache_id' => 'required|exists:taches,id',
            'parent_id' => 'nullable|exists:comments,id'
        ]);

        // Vérifier si l'utilisateur est connecté
        if (!Auth::check()) {
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json(['error' => 'Vous devez être connecté pour commenter.'], 401);
            }
            return redirect()->route('login')->with('error', 'Vous devez être connecté pour commenter.');
        }

        // Vérifier si l'utilisateur a un rôle autorisé (client, entreprise ou membre)
        $allowedRoles = ['client', 'entreprise', 'membre'];
        if (!in_array(Auth::user()->role, $allowedRoles)) {
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json(['error' => 'Vous n\'êtes pas autorisé à ajouter des commentaires.'], 403);
            }
            return redirect()->back()->with('error', 'Vous n\'êtes pas autorisé à ajouter des commentaires.');
        }

        // Créer le commentaire
        $comment = Comment::create([
            'content' => $request->content,
            'user_id' => Auth::id(),
            'tache_id' => $request->tache_id,
            'parent_id' => $request->parent_id ?: null,
            'likes_count' => 0
        ]);

        // Charger les relations pour la réponse
        $comment->load('user');

        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'success' => true,
                'comment' => $comment,
                'user_name' => $comment->user->name,
                'created_at' => $comment->created_at->format('d M Y - H:i'),
                'replies' => []
            ]);
        }

        return redirect()->back()->with('success', 'Commentaire ajouté avec succès.');
    }

    /**
     * Ajouter ou supprimer un like sur un commentaire
     */
    public function toggleLike(Request $request, $id)
    {
        // Log pour déboguer
        \Log::info('Requête reçue dans CommentController@toggleLike', [
            'content_type' => $request->header('Content-Type'),
            'id' => $id,
            'user' => Auth::check() ? Auth::user()->id : 'non connecté',
            'role' => Auth::check() ? Auth::user()->role : 'aucun',
            'method' => $request->method(),
            'all_headers' => $request->headers->all(),
            'all_data' => $request->all(),
            'is_json' => $request->isJson(),
            'ajax' => $request->ajax(),
            'wants_json' => $request->wantsJson(),
            'request_uri' => $request->getRequestUri()
        ]);

        // Vérifier si l'utilisateur est connecté
        if (!Auth::check()) {
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json(['error' => 'Vous devez être connecté pour aimer un commentaire.'], 401);
            }
            return redirect()->route('login')->with('error', 'Vous devez être connecté pour aimer un commentaire.');
        }

        // Vérifier si l'utilisateur a un rôle autorisé (entreprise, membre ou client)
        $allowedRoles = ['entreprise', 'membre', 'client'];
        $userRole = Auth::user()->role;

        \Log::info('Vérification du rôle pour like', [
            'user_role' => $userRole,
            'allowed_roles' => $allowedRoles,
            'is_allowed' => in_array($userRole, $allowedRoles)
        ]);

        if (!in_array($userRole, $allowedRoles)) {
            if ($request->ajax() || $request->wantsJson()) {
                return response()->json(['error' => 'Vous n\'êtes pas autorisé à aimer des commentaires.'], 403);
            }
            return redirect()->back()->with('error', 'Vous n\'êtes pas autorisé à aimer des commentaires.');
        }

        $comment = Comment::findOrFail($id);
        $userId = Auth::id();

        // Vérifier si l'utilisateur a déjà aimé ce commentaire
        $existingLike = CommentLike::where('user_id', $userId)
            ->where('comment_id', $id)
            ->first();

        if ($existingLike) {
            // Si le like existe, le supprimer
            $existingLike->delete();

            // Décrémenter le compteur de likes
            $comment->likes_count = max(0, $comment->likes_count - 1);
            $comment->save();

            $action = 'unliked';
        } else {
            // Sinon, créer un nouveau like
            CommentLike::create([
                'user_id' => $userId,
                'comment_id' => $id
            ]);

            // Incrémenter le compteur de likes
            $comment->likes_count++;
            $comment->save();

            $action = 'liked';
        }

        if ($request->ajax() || $request->wantsJson()) {
            return response()->json([
                'success' => true,
                'action' => $action,
                'likes_count' => $comment->likes_count
            ]);
        }

        return redirect()->back()->with('success', $action === 'liked' ? 'Commentaire aimé avec succès.' : 'Like retiré avec succès.');
    }

    /**
     * Supprimer un commentaire
     */
    public function destroy($id)
    {
        $comment = Comment::findOrFail($id);

        // Vérifier si l'utilisateur est autorisé à supprimer ce commentaire
        if (Auth::id() !== $comment->user_id && Auth::user()->role !== 'admin') {
            return response()->json(['error' => 'Non autorisé à supprimer ce commentaire.'], 403);
        }

        // Supprimer le commentaire
        $comment->delete();

        return response()->json(['success' => true]);
    }
}
