@extends($userTheme ?? 'theme')

@section('contenu')
<div class="main-content">
    <div class="page-content">
        <div class="container-fluid">
            <!-- Page Title -->
            <div class="row">
                <div class="col-12">
                    <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                        <h4 class="mb-sm-0">🎨 Système d'Avatar - Démonstration</h4>
                        <div class="page-title-right">
                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="{{ url('/dashboard') }}">Dashboard</a></li>
                                <li class="breadcrumb-item active">Avatars</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Tailles d'avatars -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Tailles d'Avatar</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center gap-3 flex-wrap">
                                <div class="text-center">
                                    {!! Auth::user()->getAvatarHtml('xs') !!}
                                    <small class="d-block mt-1">XS (24px)</small>
                                </div>
                                <div class="text-center">
                                    {!! Auth::user()->getAvatarHtml('sm') !!}
                                    <small class="d-block mt-1">SM (32px)</small>
                                </div>
                                <div class="text-center">
                                    {!! Auth::user()->getAvatarHtml('md') !!}
                                    <small class="d-block mt-1">MD (40px)</small>
                                </div>
                                <div class="text-center">
                                    {!! Auth::user()->getAvatarHtml('lg') !!}
                                    <small class="d-block mt-1">LG (56px)</small>
                                </div>
                                <div class="text-center">
                                    {!! Auth::user()->getAvatarHtml('xl') !!}
                                    <small class="d-block mt-1">XL (72px)</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Avatars avec statuts -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Avatars avec Statuts</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center gap-3 flex-wrap">
                                <div class="text-center">
                                    {!! Auth::user()->getAvatarHtml('lg', 'avatar-online') !!}
                                    <small class="d-block mt-1">En ligne</small>
                                </div>
                                <div class="text-center">
                                    {!! Auth::user()->getAvatarHtml('lg', 'avatar-busy') !!}
                                    <small class="d-block mt-1">Occupé</small>
                                </div>
                                <div class="text-center">
                                    {!! Auth::user()->getAvatarHtml('lg', 'avatar-away') !!}
                                    <small class="d-block mt-1">Absent</small>
                                </div>
                                <div class="text-center">
                                    {!! Auth::user()->getAvatarHtml('lg', 'avatar-offline') !!}
                                    <small class="d-block mt-1">Hors ligne</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Groupe d'avatars -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Groupe d'Avatars</h5>
                        </div>
                        <div class="card-body">
                            <div class="avatar-group">
                                @php
                                    $users = \App\Models\User::take(5)->get();
                                @endphp
                                @foreach($users as $user)
                                    {!! $user->getAvatarHtml('md', 'avatar-bordered') !!}
                                @endforeach
                                <div class="avatar avatar-md" style="background-color: #6c757d; color: white;">
                                    <span class="avatar-initials">+3</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Liste d'utilisateurs avec avatars -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Liste d'Utilisateurs</h5>
                        </div>
                        <div class="card-body">
                            <div class="list-group list-group-flush">
                                @php
                                    $allUsers = \App\Models\User::all();
                                @endphp
                                @foreach($allUsers as $user)
                                <div class="list-group-item d-flex align-items-center">
                                    {!! $user->getAvatarHtml('md', 'me-3') !!}
                                    <div class="flex-grow-1">
                                        <h6 class="mb-1">{{ $user->name }}</h6>
                                        <p class="mb-0 text-muted">{{ $user->email }}</p>
                                        <small class="text-muted">{{ ucfirst($user->role) }}</small>
                                    </div>
                                    <div>
                                        <span class="badge bg-{{ $user->role == 'admin' ? 'danger' : ($user->role == 'entreprise' ? 'success' : ($user->role == 'membre' ? 'info' : 'warning')) }}">
                                            {{ ucfirst($user->role) }}
                                        </span>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Avatars avec animations -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Avatars avec Animations</h5>
                        </div>
                        <div class="card-body">
                            <div class="d-flex align-items-center gap-3 flex-wrap">
                                <div class="text-center">
                                    {!! Auth::user()->getAvatarHtml('lg', 'avatar-pulse') !!}
                                    <small class="d-block mt-1">Pulsation</small>
                                </div>
                                <div class="text-center">
                                    {!! Auth::user()->getAvatarHtml('lg', 'avatar-gradient') !!}
                                    <small class="d-block mt-1">Gradient</small>
                                </div>
                                <div class="text-center">
                                    {!! Auth::user()->getAvatarHtml('lg', 'avatar-square') !!}
                                    <small class="d-block mt-1">Carré</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Code d'utilisation -->
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="card-title mb-0">Comment utiliser les avatars</h5>
                        </div>
                        <div class="card-body">
                            <h6>Dans les templates Blade :</h6>
                            <pre class="bg-light p-3 rounded"><code>{{-- Avatar simple --}}
{!! Auth::user()->getAvatarHtml() !!}

{{-- Avatar avec taille spécifique --}}
{!! Auth::user()->getAvatarHtml('lg') !!}

{{-- Avatar avec classes CSS supplémentaires --}}
{!! Auth::user()->getAvatarHtml('md', 'avatar-online me-3') !!}

{{-- Avatar pour un autre utilisateur --}}
{!! $user->getAvatarHtml('sm', 'avatar-bordered') !!}</code></pre>

                            <h6 class="mt-4">Dans le code PHP :</h6>
                            <pre class="bg-light p-3 rounded"><code>// Générer un avatar pour un utilisateur
$user->generateAvatar();

// Obtenir les initiales
$initials = AvatarHelper::getInitials($user->name);

// Obtenir une couleur basée sur le nom
$color = AvatarHelper::getColorFromName($user->name);</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
