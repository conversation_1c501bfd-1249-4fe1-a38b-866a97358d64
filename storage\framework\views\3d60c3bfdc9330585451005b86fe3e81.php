<?php $__env->startSection('contenu'); ?>
<!-- <PERSON><PERSON><PERSON> de la bibliothèque ApexCharts -->
<script src="https://cdn.jsdelivr.net/npm/apexcharts"></script>

<!-- Styles CSS personnalisés pour un design moderne -->
<style>
    .dashboard-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }
    
    .stats-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        overflow: hidden;
        position: relative;
    }
    
    .stats-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    }
    
    .stats-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
    }
    
    .chart-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }
    
    .chart-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 20px;
        border-radius: 20px 20px 0 0;
    }
    
    .counter-number {
        font-size: 2.5rem;
        font-weight: 700;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    
    .icon-wrapper {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
    }
    
    .trend-badge {
        background: rgba(34, 197, 94, 0.1);
        color: #22c55e;
        border: 1px solid rgba(34, 197, 94, 0.2);
        border-radius: 50px;
        padding: 5px 15px;
        font-size: 0.85rem;
        font-weight: 600;
    }
    
    .trend-badge.negative {
        background: rgba(239, 68, 68, 0.1);
        color: #ef4444;
        border: 1px solid rgba(239, 68, 68, 0.2);
    }
    
    .page-title {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 30px;
        margin-bottom: 30px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    }
    
    .btn-modern {
        border-radius: 50px;
        padding: 8px 20px;
        font-weight: 600;
        transition: all 0.3s ease;
        border: none;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
    }
    
    .btn-modern:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.3);
        color: white;
    }
    
    .animate-fade-in {
        animation: fadeInUp 0.8s ease-out;
    }
    
    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .animate-delay-1 { animation-delay: 0.1s; }
    .animate-delay-2 { animation-delay: 0.2s; }
    .animate-delay-3 { animation-delay: 0.3s; }
    .animate-delay-4 { animation-delay: 0.4s; }
</style>

<div class="dashboard-container">
    <div class="container-fluid">
        <!-- Page Title -->
        <div class="page-title animate-fade-in">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2" style="color: #667eea; font-weight: 700;">
                        <i class="ri-dashboard-3-line me-3"></i><?php echo e(__('message.dashboard')); ?>

                    </h1>
                    <p class="text-muted mb-0 fs-5">Bienvenue sur votre tableau de bord analytique moderne</p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="d-flex justify-content-end gap-2">
                        <span class="badge bg-primary-subtle text-primary px-3 py-2 rounded-pill">
                            <i class="ri-time-line me-1"></i>Mis à jour en temps réel
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cartes statistiques -->
        <div class="row g-4 mb-5">
            <!-- Carte Clients -->
            <div class="col-xl-3 col-md-6">
                <div class="stats-card animate-fade-in animate-delay-1">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <p class="text-muted mb-2 text-uppercase fw-semibold" style="font-size: 0.85rem; letter-spacing: 1px;">
                                    Clients
                                </p>
                                <h2 class="counter-number mb-2" data-target="<?php echo e($clientCount); ?>"><?php echo e($clientCount); ?></h2>
                                <div class="trend-badge">
                                    <i class="ri-arrow-up-line me-1"></i>+16.24%
                                </div>
                            </div>
                            <div class="icon-wrapper">
                                <i class="ri-user-heart-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Carte Administrateurs -->
            <div class="col-xl-3 col-md-6">
                <div class="stats-card animate-fade-in animate-delay-2">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <p class="text-muted mb-2 text-uppercase fw-semibold" style="font-size: 0.85rem; letter-spacing: 1px;">
                                    <?php echo e(__('message.Administrators')); ?>

                                </p>
                                <h2 class="counter-number mb-2" data-target="<?php echo e($adminCount); ?>"><?php echo e($adminCount); ?></h2>
                                <div class="trend-badge negative">
                                    <i class="ri-arrow-down-line me-1"></i>-3.96%
                                </div>
                            </div>
                            <div class="icon-wrapper">
                                <i class="ri-shield-user-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Carte Entreprises -->
            <div class="col-xl-3 col-md-6">
                <div class="stats-card animate-fade-in animate-delay-3">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <p class="text-muted mb-2 text-uppercase fw-semibold" style="font-size: 0.85rem; letter-spacing: 1px;">
                                    Entreprises
                                </p>
                                <h2 class="counter-number mb-2" data-target="<?php echo e($entrepriseCount); ?>"><?php echo e($entrepriseCount); ?></h2>
                                <div class="trend-badge">
                                    <i class="ri-arrow-up-line me-1"></i>+12.45%
                                </div>
                            </div>
                            <div class="icon-wrapper">
                                <i class="ri-building-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Carte Personnel (Membres) -->
            <div class="col-xl-3 col-md-6">
                <div class="stats-card animate-fade-in animate-delay-4">
                    <div class="card-body p-4">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <p class="text-muted mb-2 text-uppercase fw-semibold" style="font-size: 0.85rem; letter-spacing: 1px;">
                                    Personnel
                                </p>
                                <h2 class="counter-number mb-2" data-target="<?php echo e($membreCount); ?>"><?php echo e($membreCount); ?></h2>
                                <div class="trend-badge">
                                    <i class="ri-arrow-up-line me-1"></i>+8.32%
                                </div>
                            </div>
                            <div class="icon-wrapper">
                                <i class="ri-team-line"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graphiques -->
        <div class="row g-4 mb-5">
            <!-- Graphique en donut -->
            <div class="col-xl-6">
                <div class="chart-card animate-fade-in">
                    <div class="chart-header">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h5 class="mb-1 fw-bold">
                                    <i class="ri-pie-chart-line me-2"></i>Répartition des Utilisateurs par Rôle
                                </h5>
                                <p class="mb-0 opacity-75">Distribution des 4 types d'utilisateurs</p>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="ri-more-2-line"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="#"><?php echo e(__('message.All')); ?></a></li>
                                    <li><a class="dropdown-item" href="#"><?php echo e(__('message.This Month')); ?></a></li>
                                    <li><a class="dropdown-item" href="#">Cette année</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <div id="user_device_pie_charts" style="min-height: 350px;"></div>
                    </div>
                </div>
            </div>

            <!-- Histogramme des tâches -->
            <div class="col-xl-6">
                <div class="chart-card animate-fade-in">
                    <div class="chart-header">
                        <div class="d-flex align-items-center justify-content-between">
                            <div>
                                <h5 class="mb-1 fw-bold">
                                    <i class="ri-bar-chart-grouped-line me-2"></i><?php echo e(__('message.Task Distribution by Project')); ?>

                                </h5>
                                <p class="mb-0 opacity-75">Top 5 des projets avec le plus de tâches</p>
                            </div>
                            <div class="dropdown">
                                <button class="btn btn-light btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="ri-more-2-line"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item active" href="#">Top 5 projets</a></li>
                                    <li><a class="dropdown-item" href="#">Projets actifs</a></li>
                                    <li><a class="dropdown-item" href="#">Projets récents</a></li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <div class="d-flex justify-content-center gap-4 mb-3">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-success me-2" style="width:12px; height:12px; border-radius: 50%;"></span>
                                <small class="text-muted fw-semibold"><?php echo e(__('message.Completed')); ?></small>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-warning me-2" style="width:12px; height:12px; border-radius: 50%;"></span>
                                <small class="text-muted fw-semibold"><?php echo e(__('message.In Progress')); ?></small>
                            </div>
                            <div class="d-flex align-items-center">
                                <span class="badge bg-danger me-2" style="width:12px; height:12px; border-radius: 50%;"></span>
                                <small class="text-muted fw-semibold"><?php echo e(__('message.Pending')); ?></small>
                            </div>
                        </div>
                        <div id="projects_tasks_chart" style="min-height: 300px;"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scripts pour les graphiques avec animations -->
<script>
document.addEventListener("DOMContentLoaded", function () {
    // Animation des compteurs
    const counters = document.querySelectorAll('.counter-number');
    counters.forEach(counter => {
        const target = parseInt(counter.getAttribute('data-target'));
        let current = 0;
        const increment = target / 50;
        const timer = setInterval(() => {
            current += increment;
            if (current >= target) {
                counter.textContent = target;
                clearInterval(timer);
            } else {
                counter.textContent = Math.floor(current);
            }
        }, 30);
    });

    // Configuration du graphique en donut
    var donutOptions = {
        chart: {
            type: "donut",
            height: 350,
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800,
                animateGradually: {
                    enabled: true,
                    delay: 150
                }
            }
        },
        series: [<?php echo e($adminCount); ?>, <?php echo e($entrepriseCount); ?>, <?php echo e($membreCount); ?>, <?php echo e($clientCount); ?>],
        labels: ["Administrateurs", "Entreprises", "Personnel", "Clients"],
        colors: ["#dc3545", "#28a745", "#17a2b8", "#ffc107"],
        legend: {
            position: "bottom",
            horizontalAlign: "center",
            fontSize: '14px',
            fontWeight: 600,
            markers: {
                width: 12,
                height: 12,
                radius: 6
            }
        },
        plotOptions: {
            pie: {
                donut: {
                    size: '70%',
                    labels: {
                        show: true,
                        name: {
                            show: true,
                            fontSize: '16px',
                            fontWeight: 600,
                            color: '#364a63'
                        },
                        value: {
                            show: true,
                            fontSize: '24px',
                            fontWeight: 700,
                            color: '#667eea'
                        },
                        total: {
                            show: true,
                            label: '<?php echo e(__("message.Total")); ?>',
                            fontSize: '14px',
                            fontWeight: 600,
                            color: '#8c9097',
                            formatter: function (w) {
                                return w.globals.seriesTotals.reduce((a, b) => a + b, 0);
                            }
                        }
                    }
                }
            }
        },
        dataLabels: {
            enabled: true,
            style: {
                fontSize: '12px',
                fontWeight: 'bold'
            }
        },
        tooltip: {
            theme: 'dark',
            y: {
                formatter: function(val) {
                    return val + " éléments"
                }
            }
        }
    };

    // Configuration de l'histogramme des tâches par projet
    var barOptions = {
        chart: {
            type: 'bar',
            height: 300,
            stacked: true,
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: false,
                    zoom: false,
                    zoomin: false,
                    zoomout: false,
                    pan: false,
                    reset: false
                }
            },
            animations: {
                enabled: true,
                easing: 'easeinout',
                speed: 800
            }
        },
        plotOptions: {
            bar: {
                horizontal: false,
                borderRadius: 8,
                columnWidth: '60%',
                dataLabels: {
                    total: {
                        enabled: true,
                        style: {
                            fontSize: '12px',
                            fontWeight: 600,
                            color: '#364a63'
                        }
                    }
                }
            },
        },
        xaxis: {
            categories: <?php echo json_encode($projectNames); ?>,
            labels: {
                style: {
                    fontSize: '12px',
                    fontWeight: 500,
                    colors: '#8c9097'
                },
                rotate: -45
            }
        },
        yaxis: {
            title: {
                text: 'Nombre de tâches',
                style: {
                    fontSize: '14px',
                    fontWeight: 600,
                    color: '#8c9097'
                }
            }
        },
        legend: {
            position: 'bottom',
            fontSize: '14px',
            fontWeight: 500
        },
        fill: {
            opacity: 0.9,
            type: 'gradient',
            gradient: {
                shade: 'light',
                type: 'vertical',
                shadeIntensity: 0.25,
                opacityFrom: 0.9,
                opacityTo: 0.7
            }
        },
        colors: ['#34c38f', '#f1b44c', '#f46a6a'],
        series: [{
            name: '<?php echo e(__("message.Completed")); ?>',
            data: <?php echo json_encode($completedTasks); ?>

        }, {
            name: '<?php echo e(__("message.In Progress")); ?>',
            data: <?php echo json_encode($inProgressTasks); ?>

        }, {
            name: '<?php echo e(__("message.Pending")); ?>',
            data: <?php echo json_encode($pendingTasks); ?>

        }],
        dataLabels: {
            enabled: false
        },
        tooltip: {
            theme: 'dark',
            y: {
                formatter: function (val) {
                    return val + " tâche(s)"
                }
            }
        }
    };

    // Création et rendu des graphiques
    try {
        var donutChart = new ApexCharts(document.querySelector("#user_device_pie_charts"), donutOptions);
        donutChart.render();

        var barChart = new ApexCharts(document.querySelector("#projects_tasks_chart"), barOptions);
        barChart.render();

        console.log("✅ Graphiques initialisés avec succès");
    } catch (error) {
        console.error("❌ Erreur lors de l'initialisation des graphiques:", error);
    }
});
</script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make($userTheme ?? 'theme', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\task\resources\views/dashboard.blade.php ENDPATH**/ ?>