<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Vérifier si la colonne project_description n'existe pas déjà
        if (!Schema::hasColumn('projets', 'project_description')) {
            Schema::table('projets', function (Blueprint $table) {
                $table->text('project_description')->nullable()->after('project_name');
            });
        }

        // Copier les données de task_description vers project_description si les colonnes existent
        if (Schema::hasColumn('projets', 'task_description') && Schema::hasColumn('projets', 'project_description')) {
            DB::statement('UPDATE projets SET project_description = task_description WHERE task_description IS NOT NULL');
        }

        // Supprimer les anciennes colonnes si elles existent
        Schema::table('projets', function (Blueprint $table) {
            if (Schema::hasColumn('projets', 'task_title')) {
                $table->dropColumn('task_title');
            }
            if (Schema::hasColumn('projets', 'task_description')) {
                $table->dropColumn('task_description');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Recréer les anciennes colonnes
        Schema::table('projets', function (Blueprint $table) {
            $table->string('task_title')->after('project_name');
            $table->text('task_description')->nullable()->after('task_title');
        });

        // Copier les données de retour
        DB::statement('UPDATE projets SET task_description = project_description WHERE project_description IS NOT NULL');

        // Supprimer la nouvelle colonne
        Schema::table('projets', function (Blueprint $table) {
            $table->dropColumn('project_description');
        });
    }
};
