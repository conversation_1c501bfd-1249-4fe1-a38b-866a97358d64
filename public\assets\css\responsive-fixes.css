/* Responsive Fixes CSS - Optimisation pour écrans PC */

/* Réduction des hauteurs inutiles et optimisation de l'espace vertical */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Ajustement du conteneur principal */
#layout-wrapper {
    display: flex;
    flex-direction: column;
    flex: 1;
}

/* Ajustement du contenu principal */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* Optimisation de la page-content */
.page-content {
    flex: 1;
    padding-top: 1rem;
    padding-bottom: 1rem;
}

/* Réduction des marges et paddings excessifs */
.container-fluid {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

/* Ajustement des cartes */
.card {
    margin-bottom: 1rem;
}

.card-body {
    padding: 1rem;
}

/* Optimisation des en-têtes de page */
.page-title-box {
    padding-bottom: 0.5rem;
}

/* Réduction de la hauteur des bannières */
.position-relative .bg-image,
.position-relative [style*="background-image"] {
    height: auto !important;
    min-height: 150px !important;
}

/* Optimisation du footer */
.footer {
    padding: 0.75rem 0;
    margin-top: auto;
}

/* Ajustements pour les tableaux */
.table td, .table th {
    padding: 0.5rem 0.75rem;
}

/* Optimisation des tabs */
.nav-tabs .nav-link {
    padding: 0.5rem 1rem;
}

/* Ajustements pour les formulaires */
.form-group {
    margin-bottom: 0.75rem;
}

/* Optimisation des espaces entre les sections */
.mb-4 {
    margin-bottom: 1rem !important;
}

.py-4 {
    padding-top: 1rem !important;
    padding-bottom: 1rem !important;
}

/* Ajustements pour les écrans larges */
@media (min-width: 1200px) {
    .container {
        max-width: 95%;
    }
    
    /* Réduction des marges latérales sur grands écrans */
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}

/* Ajustements pour les écrans moyens */
@media (min-width: 768px) and (max-width: 1199px) {
    .container {
        max-width: 98%;
    }
}

/* Optimisation des hauteurs d'images de couverture */
[style*="height: 250px"] {
    height: 180px !important;
}

/* Réduction des espaces dans les breadcrumbs */
.breadcrumb {
    margin-bottom: 0.5rem;
    padding: 0.25rem 0;
}

/* Optimisation des alertes */
.alert {
    padding: 0.5rem 1rem;
    margin-bottom: 0.75rem;
}

/* Ajustement des boutons */
.btn {
    padding: 0.375rem 0.75rem;
}

/* Optimisation des avatars et images */
.img-thumbnail {
    width: 100px !important;
    height: 100px !important;
}
