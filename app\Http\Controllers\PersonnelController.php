<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Hash;
use Illuminate\Http\Request;
use App\Models\User;

class PersonnelController extends Controller
{
    // Récupérer tous les personnels
    public function getPersonnel()
    {
        // Vérifier si l'utilisateur est connecté
        if (!auth()->check()) {
            return redirect('/login');
        }

        // Si l'utilisateur est une entreprise, montrer seulement les membres qu'il a ajoutés
        if (auth()->user()->role == 'entreprise') {
            $personnels = \DB::table('users')
                            ->where('role', 'membre')
                            ->where('entreprise_id', auth()->id())
                            ->get();
        } else {
            // Pour les admins, montrer tous les membres
            $personnels = \DB::table('users')
                            ->where('role', 'membre')
                            ->get();
        }

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('liste-personnel', ['data' => $personnels])->with('userTheme', $theme);
    }

    // Modifier un personnel
    public function updatePersonnel(Request $req)
    {
        $personnel = User::find($req->id);
        if ($personnel) {
            // Vérifier si l'utilisateur est autorisé à modifier ce membre
            if (auth()->user()->role == 'entreprise' && $personnel->entreprise_id != auth()->id()) {
                return redirect('liste-personnel')->with('error', 'Vous n\'êtes pas autorisé à modifier ce membre.');
            }

            $personnel->name = $req->nom;
            $personnel->email = $req->email;
            $personnel->poste = $req->poste;
            if ($req->password) {
                $personnel->password = Hash::make($req->password);
            }
            $personnel->save();
            return redirect('liste-personnel')->with('message', 'Modification réussie');
        } else {
            return redirect('liste-personnel')->with('error', 'Personnel non trouvé');
        }
    }

    // Ajouter un nouveau personnel
    public function addPersonnel(Request $req)
    {
        // Vérifier si l'utilisateur est connecté
        if (!auth()->check()) {
            return redirect('/login');
        }

        $req->validate([
            'nom' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:6|confirmed',
            'poste' => 'required|string|max:255',
        ]);

        \DB::table('users')->insert([
            'name' => $req->nom,
            'email' => $req->email,
            'password' => Hash::make($req->password),
            'poste' => $req->poste,
            'role' => 'membre',
            'entreprise_id' => auth()->id(),
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return redirect('liste-personnel')->with('message', 'Ajout de personnel réussi');
    }

    // Récupérer un personnel pour modification
    public function getPersonnelId($id)
    {
        // Vérifier si l'utilisateur est connecté
        if (!auth()->check()) {
            return redirect('/login');
        }

        $personnel = User::find($id);

        // Vérifier si l'utilisateur est autorisé à modifier ce membre
        if (auth()->user()->role == 'entreprise' && $personnel->entreprise_id != auth()->id()) {
            return redirect('liste-personnel')->with('error', 'Vous n\'êtes pas autorisé à modifier ce membre.');
        }

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('modifier-personnel', ['data' => $personnel])->with('userTheme', $theme);
    }

    // Supprimer un personnel
    public function deletePersonnel($id)
    {
        $personnel = User::find($id);
        if ($personnel) {
            // Vérifier si l'utilisateur est autorisé à supprimer ce membre
            if (auth()->user()->role == 'entreprise' && $personnel->entreprise_id != auth()->id()) {
                return redirect('liste-personnel')->with('error', 'Vous n\'êtes pas autorisé à supprimer ce membre.');
            }

            $personnel->delete();
            return redirect('/liste-personnel')->with('message', 'Un personnel a été bien supprimé');
        } else {
            return redirect('/liste-personnel')->with('error', 'Personnel non trouvé');
        }
    }
}
