<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8" />
    <title>Paiement | TaskFlow</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="TaskFlow - Gestion de projets et de tâches" name="description" />
    <meta content="TaskFlow" name="author" />

    <!-- App favicon -->
    <link rel="shortcut icon" href="{{ asset('assets/images/favicon.ico') }}">

    <!-- Bootstrap Css -->
    <link href="{{ asset('assets/css/bootstrap.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- Icons Css -->
    <link href="{{ asset('assets/css/icons.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- App Css-->
    <link href="{{ asset('assets/css/app.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- Custom Css-->
    <link href="{{ asset('assets/css/custom.min.css') }}" rel="stylesheet" type="text/css" />

    <style>
        body {
            background-color: #f5f7fb;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
            font-family: 'Poppins', sans-serif;
        }

        .checkout-container {
            max-width: 800px;
            width: 100%;
            margin: 0 auto;
        }

        .checkout-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .checkout-header img {
            max-width: 180px;
            margin-bottom: 15px;
        }

        .checkout-header h3 {
            color: #364a63;
            font-weight: 600;
        }

        .checkout-header p {
            color: #526484;
        }

        .checkout-card {
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(31, 45, 61, 0.07);
            margin-bottom: 20px;
            border: 1px solid #e5e9f2;
            animation: fadeIn 0.5s ease;
        }

        .checkout-card-header {
            background-color: #6576ff;
            color: white;
            padding: 15px 20px;
            font-weight: 600;
            border-bottom: 1px solid #e5e9f2;
        }

        .checkout-card-body {
            padding: 20px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            border-radius: 6px;
            transition: all 0.3s ease;
            background-color: #f5f7fb;
            border: 1px solid #e5e9f2;
        }

        .feature-item:hover {
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(31, 45, 61, 0.1);
            border-color: #6576ff;
        }

        .feature-icon {
            width: 46px;
            height: 46px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            color: white;
        }

        .primary-gradient {
            background-color: #6576ff;
        }

        .success-gradient {
            background-color: #1ee0ac;
        }

        .info-gradient {
            background-color: #09c2de;
        }

        .danger-gradient {
            background-color: #e85347;
        }

        .btn-payment {
            background-color: #6576ff;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 4px;
            font-weight: 600;
            transition: all 0.3s ease;
            letter-spacing: 0.02em;
        }

        .btn-payment:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(101, 118, 255, 0.3);
            background-color: #5664e0;
        }

        .payment-method-icon {
            background-color: white;
            padding: 12px 15px;
            border-radius: 6px;
            box-shadow: 0 3px 10px rgba(31, 45, 61, 0.05);
            border: 1px solid #e5e9f2;
        }

        .secure-badge {
            display: inline-block;
            margin-top: 15px;
            padding: 8px 15px;
            background-color: #f5f7fb;
            border-radius: 4px;
            border: 1px solid #e5e9f2;
            color: #526484;
        }

        .footer-info {
            text-align: center;
            margin-top: 20px;
        }

        .footer-info .alert {
            background-color: #f5f7fb;
            border: 1px solid #e5e9f2;
            color: #526484;
            border-radius: 4px;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .premium-badge {
            background-color: #f4bd0e;
            color: #fff;
            padding: 4px 12px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 12px;
            display: inline-block;
            margin-left: 10px;
            text-transform: uppercase;
        }

        .table-total {
            font-size: 1rem;
            color: #364a63;
        }

        .table-total .text-primary {
            color: #6576ff !important;
            font-weight: 600;
        }

        h6 {
            color: #364a63;
            font-weight: 600;
        }

        .text-muted {
            color: #526484 !important;
        }
    </style>
</head>

<body>
    <div class="checkout-container">
        <div class="checkout-header">
            <img src="{{ asset('assets/images/newnewnew.png') }}" alt="TaskFlow Logo">
            <h3>Activez votre compte TaskFlow</h3>
            <p>Finalisez votre inscription en procédant au paiement</p>
        </div>

        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show mb-4" role="alert">
                <i class="ri-checkbox-circle-line me-2"></i> {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert">
                <i class="ri-error-warning-line me-2"></i> {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <!-- Détails de l'abonnement -->
        <div class="checkout-card">
            <div class="checkout-card-header d-flex align-items-center">
                <i class="ri-vip-crown-fill fs-20 me-2"></i>
                <span>Abonnement Premium</span>
                <span class="premium-badge ms-2">
                    <i class="ri-star-fill me-1"></i> RECOMMANDÉ
                </span>
            </div>
            <div class="checkout-card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="feature-item">
                            <div class="feature-icon primary-gradient">
                                <i class="ri-dashboard-2-line fs-20"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Tableau de bord personnalisé</h6>
                                <p class="text-muted mb-0 small">Visualisez et gérez vos projets efficacement</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="feature-item">
                            <div class="feature-icon success-gradient">
                                <i class="ri-stack-line fs-20"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Projets illimités</h6>
                                <p class="text-muted mb-0 small">Créez autant de projets que nécessaire</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="feature-item">
                            <div class="feature-icon info-gradient">
                                <i class="ri-layout-masonry-line fs-20"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Tableau Kanban interactif</h6>
                                <p class="text-muted mb-0 small">Organisez vos tâches avec une interface intuitive</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="feature-item">
                            <div class="feature-icon danger-gradient">
                                <i class="ri-customer-service-2-line fs-20"></i>
                            </div>
                            <div>
                                <h6 class="mb-1">Support client prioritaire</h6>
                                <p class="text-muted mb-0 small">Assistance dédiée pour répondre à vos besoins</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Récapitulatif de commande -->
        <div class="checkout-card">
            <div class="checkout-card-header">
                <i class="ri-shopping-cart-2-line me-2"></i> Récapitulatif de votre commande
            </div>
            <div class="checkout-card-body">
                <div class="table-responsive">
                    <table class="table table-borderless mb-0 table-total">
                        <tbody>
                            <tr>
                                <td>Abonnement Premium</td>
                                <td class="text-end">100 dinars</td>
                            </tr>
                            <tr class="border-top">
                                <th>Total</th>
                                <th class="text-end text-primary">100 dinars</th>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="mt-4">
                    <form action="/checkout/session" method="POST">
                        @csrf
                        <button type="submit" class="btn btn-payment w-100 py-3" id="payment-button">
                            <i class="ri-secure-payment-line me-2"></i> Payer 100 dinars et activer mon compte
                        </button>
                    </form>
                </div>

                <div class="text-center mt-4">
                    <div class="payment-method-icon mx-auto" style="width: fit-content;">
                        <img src="{{ asset('assets/images/stripe.svg') }}" alt="Stripe" height="40">
                    </div>

                    <div class="secure-badge mt-3">
                        <i class="ri-shield-check-line text-success me-1"></i>
                        <span class="fw-medium">Paiement sécurisé via Stripe</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer-info">
            <div class="alert alert-light py-2">
                <i class="ri-information-line me-1 align-middle"></i>
                Après le paiement, vous serez automatiquement redirigé vers votre tableau de bord.
            </div>
        </div>
    </div>

    <!-- JAVASCRIPT -->
    <script src="{{ asset('assets/libs/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('assets/libs/simplebar/simplebar.min.js') }}"></script>
    <script src="{{ asset('assets/libs/node-waves/waves.min.js') }}"></script>
    <script src="{{ asset('assets/libs/feather-icons/feather.min.js') }}"></script>
    <script src="{{ asset('assets/js/pages/plugins/lord-icon-2.1.0.js') }}"></script>
    <script src="{{ asset('assets/js/plugins.js') }}"></script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Animation des éléments de fonctionnalités
            const featureItems = document.querySelectorAll('.feature-item');
            featureItems.forEach(item => {
                item.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-5px)';
                    this.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.1)';
                });

                item.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                    this.style.boxShadow = 'none';
                });
            });

            // Animation du bouton de paiement
            const paymentButton = document.getElementById('payment-button');
            paymentButton.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px)';
                this.style.boxShadow = '0 10px 20px rgba(0, 0, 0, 0.1)';
            });

            paymentButton.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0)';
                this.style.boxShadow = 'none';
            });
        });
    </script>
</body>
</html>