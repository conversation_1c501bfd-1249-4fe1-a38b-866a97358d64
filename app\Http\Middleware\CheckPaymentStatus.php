<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class CheckPaymentStatus
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Vérifier si l'utilisateur est connecté
        if (Auth::check()) {
            $user = Auth::user();

            // Si l'utilisateur est un client ou une entreprise et n'a pas payé
            if (($user->role === 'client' || $user->role === 'entreprise') && $user->paiement !== 1) {
                // Exclure la page de paiement et les routes liées au paiement pour éviter les redirections en boucle
                if (!$request->is('checkout') &&
                    !$request->is('checkout/*') &&
                    !$request->is('set-password/*') &&
                    !$request->is('confirm-invitation/*')) {

                    return redirect('/checkout')->with('error', 'Veuillez effectuer le paiement pour accéder à cette page.');
                }
            }
        }

        return $next($request);
    }
}
