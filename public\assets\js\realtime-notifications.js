/**
 * Gestion des notifications en temps réel avec <PERSON>
 */
document.addEventListener('DOMContentLoaded', function() {
    // Vérifier si Echo est disponible
    if (typeof window.Echo === 'undefined') {
        console.error('Laravel Echo n\'est pas disponible');
        return;
    }

    // Récupérer l'ID de l'utilisateur connecté
    const userId = document.querySelector('meta[name="user-id"]')?.getAttribute('content');
    if (!userId) {
        console.error('ID utilisateur non trouvé');
        return;
    }

    // Écouter le canal privé de l'utilisateur
    window.Echo.private(`user.${userId}`)
        .listen('TaskCreated', (e) => {
            console.log('Nouvelle tâche créée:', e);
            // Mettre à jour le compteur de notifications
            updateNotificationCount();
            // Afficher une notification dans le navigateur
            showBrowserNotification('Nouvelle tâche', `Une nouvelle tâche "${e.nom}" a été créée.`);
        })
        .listen('TaskUpdated', (e) => {
            console.log('Tâche mise à jour:', e);
            // Mettre à jour le compteur de notifications
            updateNotificationCount();
            // Afficher une notification dans le navigateur
            showBrowserNotification('Tâche mise à jour', `La tâche "${e.nom}" a été mise à jour.`);
        });

    // Fonction pour mettre à jour le compteur de notifications
    function updateNotificationCount() {
        fetch('/notifications/count')
            .then(response => response.json())
            .then(data => {
                const badge = document.querySelector('#userNotifContainer');
                if (badge) {
                    badge.textContent = data.count;
                    if (data.count === 0) {
                        badge.style.display = 'none';
                    } else {
                        badge.style.display = 'inline-block';
                    }
                }
            })
            .catch(error => console.error('Error:', error));
    }

    // Fonction pour afficher une notification dans le navigateur
    function showBrowserNotification(title, body) {
        // Vérifier si les notifications sont supportées
        if (!("Notification" in window)) {
            console.error("Ce navigateur ne supporte pas les notifications desktop");
            return;
        }

        // Vérifier si l'autorisation a déjà été accordée
        if (Notification.permission === "granted") {
            // Créer et afficher la notification
            new Notification(title, { body: body, icon: '/assets/images/favicon.ico' });
        }
        // Sinon, demander la permission
        else if (Notification.permission !== "denied") {
            Notification.requestPermission().then(function (permission) {
                // Si l'utilisateur accepte, créer et afficher la notification
                if (permission === "granted") {
                    new Notification(title, { body: body, icon: '/assets/images/favicon.ico' });
                }
            });
        }
    }
});
