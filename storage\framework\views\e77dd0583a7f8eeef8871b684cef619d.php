
<?php $__env->startSection('contenu'); ?>

<!-- début du titre de la page -->
<!-- fin du titre de la page -->

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header align-items-center d-flex">
                <h4 class="card-title mb-0 flex-grow-1"><?php echo e(__('message.Add project')); ?></h4>
                <div class="flex-shrink-0">
                </div>
            </div>
            <div class="card-body">
                <div class="live-preview">
                    <div class="row gy-4">
                        <form action="/ajoutprojet" method="POST" enctype="multipart/form-data">
                            <?php echo csrf_field(); ?>

                            <div class="col-xxl-12 col-md-12 mb-3">
                                <label for="labelInput" class="form-label"><?php echo e(__('message.project_name')); ?></label>
                                <input type="text" class="form-control" id="labelInput" name="project_name">
                            </div>

                            <div class="col-xxl-12 col-md-12 mb-3">
                                <label for="projectDescription" class="form-label"><?php echo e(__('message.project_description')); ?></label>
                                <textarea class="form-control" id="projectDescription" name="project_description" rows="3" placeholder="<?php echo e(__('message.Enter project description')); ?>"></textarea>
                            </div>

                            <div class="col-xxl-12 col-md-12 mb-3">
                                <label for="startDate" class="form-label"><?php echo e(__('message.start_date')); ?></label>
                                <input type="date" class="form-control" id="startDate" name="start_date">
                            </div>

                            <div class="col-xxl-12 col-md-12 mb-3">
                                <label for="endDate" class="form-label"><?php echo e(__('message.end_date')); ?></label>
                                <input type="date" class="form-control" id="endDate" name="end_date">
                            </div>

                            <div class="col-xxl-12 col-md-12 mb-3">
                                <label for="task_image" class="form-label"><?php echo e(__('message.image')); ?> (<?php echo e(__('message.optional')); ?>)</label>
                                <input type="file" class="form-control" id="task_image" name="task_image" accept="image/*">
                            </div>

                            <div class="col-xxl-12 col-md-12 mb-3">
                                <label class="form-label"><?php echo e(__('message.Add Team Members')); ?></label>
                                <div class="border rounded p-3" style="max-height: 200px; overflow-y: auto;">
                                    <?php if(isset($membres) && count($membres) > 0): ?>
                                        <?php $__currentLoopData = $membres; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $membre): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" name="members[]" value="<?php echo e($membre->id); ?>" id="membre-<?php echo e($membre->id); ?>">
                                            <label class="form-check-label d-flex align-items-center" for="membre-<?php echo e($membre->id); ?>">
                                                <div class="avatar avatar-xs me-2" style="background-color: #4ECDC4; color: white; width: 24px; height: 24px; border-radius: 50%; display: flex; align-items: center; justify-content: center; font-size: 12px;">
                                                    <?php echo e(strtoupper(substr($membre->name, 0, 1))); ?>

                                                </div>
                                                <span><?php echo e($membre->name); ?> - <?php echo e($membre->poste ?? 'N/A'); ?></span>
                                            </label>
                                        </div>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <?php else: ?>
                                        <p class="text-muted mb-0"><?php echo e(__('message.No members available for this company')); ?></p>
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="col-xxl-12 col-md-12">
                                <button type="submit" class="btn btn-primary"><?php echo e(__('message.Add project')); ?></button>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="d-none code-view">
                    <pre class="language-markup" style="height: 450px;">
<code>&lt;!-- Champ de saisie de base --&gt;
                    </code></pre>
                </div>
            </div>
        </div>
    </div>
    <!-- fin de col -->
</div>
<!-- fin de row -->

<?php $__env->stopSection(); ?>
<?php echo $__env->make($userTheme ?? 'theme', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\task\resources\views/projet.blade.php ENDPATH**/ ?>