<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" data-layout="vertical" data-topbar="light" data-sidebar="dark" data-sidebar-size="lg" data-sidebar-image="none" data-preloader="disable" data-theme="default" data-theme-colors="default">

<head>
    <meta charset="utf-8" />
    <title>@yield('title', 'TaskFlow')</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="TaskFlow - Gestion de projets et de tâches">
    <meta name="author" content="TaskFlow">

    <!-- App favicon -->
    <link rel="shortcut icon" href="{{ asset('assets/images/favicon.ico') }}">

    <!-- Layout config Js -->
    <script src="{{ asset('assets/js/layout.js') }}"></script>

    <!-- Bootstrap Css -->
    <link href="{{ asset('assets/css/bootstrap.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- Icons Css -->
    <link href="{{ asset('assets/css/icons.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- App Css-->
    <link href="{{ asset('assets/css/app.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- Custom Css-->
    <link href="{{ asset('assets/css/custom.min.css') }}" rel="stylesheet" type="text/css" />

    <!-- Responsive fixes CSS -->
    <link href="{{ asset('assets/css/responsive-fixes.css') }}" rel="stylesheet" type="text/css" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />

    <!-- Animate.css -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />

    <!-- Custom styles -->
    <style>
        :root {
            --primary-gradient: linear-gradient(135deg, #4b79a1, #283e51);
            --secondary-gradient: linear-gradient(135deg, #f8f9fa, #e9ecef);
            --accent-gradient: linear-gradient(135deg, #FFD700, #FFA500);
        }

        .auth-one-bg {
            background-image: var(--primary-gradient);
            background-position: center;
            background-size: cover;
        }

        .auth-page-wrapper {
            min-height: 100vh;
            position: relative;
            display: flex;
            flex-direction: column;
        }

        .auth-page-content {
            flex-grow: 1;
            z-index: 2;
        }

        .card {
            border: none;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .card:hover {
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .btn-primary, .bg-primary {
            background: var(--primary-gradient) !important;
            border: none;
        }

        .btn-primary:hover {
            opacity: 0.95;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }

        .text-primary {
            color: #4b79a1 !important;
        }

        .custom-card-header {
            background: var(--primary-gradient);
            color: white;
            padding: 1.2rem;
            border-radius: 0.5rem 0.5rem 0 0;
        }

        .feature-icon {
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            color: white;
            transition: all 0.3s ease;
        }

        .feature-item {
            transition: all 0.3s ease;
            border-radius: 0.5rem;
        }

        .feature-item:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .custom-badge {
            background: var(--accent-gradient);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 2rem;
            font-weight: bold;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .auth-logo img {
                height: 30px;
            }

            .feature-item {
                padding: 0.75rem !important;
            }

            .feature-icon {
                width: 40px;
                height: 40px;
            }
        }
    </style>

    @yield('custom_css')
</head>

<body>
    <div class="auth-page-wrapper">
        <!-- Auth page background -->
        <div class="auth-one-bg-position auth-one-bg" id="auth-particles">
            <div class="bg-overlay" style="background-color: rgba(0, 0, 0, 0.4);"></div>

            <div class="shape">
                <svg xmlns="http://www.w3.org/2000/svg" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink" viewBox="0 0 1440 120">
                    <path d="M 0,36 C 144,53.6 432,123.2 720,124 C 1008,124.8 1296,56.8 1440,40L1440 140L0 140z"></path>
                </svg>
            </div>
        </div>

        <!-- Auth page content -->
        <div class="auth-page-content">
            <div class="container">
                <!-- Logo and tagline -->
                <div class="row">
                    <div class="col-lg-12">
                        <div class="text-center mt-sm-5 mb-4 text-white animate__animated animate__fadeIn">
                            <div>
                                <a href="/" class="d-inline-block auth-logo">
                                    <img src="{{ asset('assets/images/newnewnew.png') }}" alt="TaskFlow Logo" height="50">
                                </a>
                            </div>
                            <p class="mt-3 fs-15 fw-medium animate__animated animate__fadeIn animate__delay-1s">
                                " Le bon flow pour chaque tâche. "
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Main content area - dynamically sized based on page needs -->
                @hasSection('full_content')
                    @yield('full_content')
                @else
                    <div class="row justify-content-center">
                        <div class="@yield('card_width', 'col-md-8 col-lg-6 col-xl-5')">
                            <div class="card mt-4 overflow-hidden animate__animated animate__fadeIn animate__faster">
                                @hasSection('card_header')
                                    <div class="custom-card-header text-center">
                                        @yield('card_header')
                                    </div>
                                @endif

                                <div class="card-body p-4">
                                    @hasSection('card_title')
                                        <div class="text-center mb-4">
                                            @yield('card_title')
                                        </div>
                                    @endif

                                    <!-- Flash messages -->
                                    @if(session('status'))
                                        <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
                                            <i class="fas fa-check-circle me-1 align-middle"></i> {{ session('status') }}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                        </div>
                                    @endif

                                    @if(session('error'))
                                        <div class="alert alert-danger alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
                                            <i class="fas fa-exclamation-circle me-1 align-middle"></i> {{ session('error') }}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                        </div>
                                    @endif

                                    @if(session('message') || session('success'))
                                        <div class="alert alert-success alert-dismissible fade show animate__animated animate__fadeIn" role="alert">
                                            <i class="fas fa-check-circle me-1 align-middle"></i> {{ session('message') ?? session('success') }}
                                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                        </div>
                                    @endif

                                    <!-- Main content -->
                                    <div class="p-2">
                                        @yield('contenu')
                                    </div>
                                </div>

                                @hasSection('card_footer')
                                    <div class="card-footer py-3 text-center" style="background-color: #f8f9fa; border-top: 1px solid rgba(0,0,0,0.05);">
                                        @yield('card_footer')
                                    </div>
                                @endif
                            </div>

                            @hasSection('below_card')
                                <div class="mt-4 text-center">
                                    @yield('below_card')
                                </div>
                            @else
                                <div class="mt-4 text-center">
                                    <p class="mb-0 bg-white text-dark py-2 px-4 rounded-pill d-inline-block shadow-sm fw-medium">
                                        {{ __('message.Don\'t have an account') }} ?
                                        <a href="/register" class="fw-bold text-primary ms-1">
                                            {{ __('message.Signup') }} <i class="ri-arrow-right-line align-middle"></i>
                                        </a>
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                @endif
            </div>
        </div>

        <!-- Footer -->
        <footer class="footer">
            <div class="container">
                <div class="row">
                    <div class="col-lg-12 text-center text-white-50">
                        <p class="mb-0">&copy; {{ date('Y') }} TaskFlow. Tous droits réservés.</p>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- JAVASCRIPT -->
    <script src="{{ asset('assets/libs/bootstrap/js/bootstrap.bundle.min.js') }}"></script>
    <script src="{{ asset('assets/libs/simplebar/simplebar.min.js') }}"></script>
    <script src="{{ asset('assets/libs/node-waves/waves.min.js') }}"></script>
    <script src="{{ asset('assets/libs/feather-icons/feather.min.js') }}"></script>
    <script src="{{ asset('assets/js/pages/plugins/lord-icon-2.1.0.js') }}"></script>
    <script src="{{ asset('assets/js/plugins.js') }}"></script>

    <!-- Particles JS -->
    <script src="{{ asset('assets/libs/particles.js/particles.js') }}"></script>
    <script src="{{ asset('assets/js/pages/particles.app.js') }}"></script>

    <!-- Password addon init -->
    <script src="{{ asset('assets/js/pages/password-addon.init.js') }}"></script>

    @yield('scripts')
</body>


</html>