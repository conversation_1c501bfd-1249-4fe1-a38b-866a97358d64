@extends('registertheme')

@section('contenu')
<div class="card-body p-4">
    <div class="text-center mt-2">
        <h5 class="text-primary fw-bold mb-4 animate__animated animate__fadeInDown">{{ __('auth.Create Your Account') }}</h5>
    </div>

    <div class="p-2 mt-4">
        <form method="POST" action="{{ route('register') }}" class="animate__animated animate__fadeIn animate__delay-1s">
            @csrf

            {{-- Name --}}
            <div class="mb-4">
                <div class="form-floating">
                    <input id="name" type="text"
                        class="form-control @error('name') is-invalid @enderror"
                        name="name" value="{{ old('name') }}"
                        required autofocus placeholder="{{ __('auth.Name') }}">
                    <label for="name">{{ __('auth.Name') }}</label>
                    @error('name')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            {{-- Email --}}
            <div class="mb-4">
                <div class="form-floating">
                    <input id="email" type="email"
                        class="form-control @error('email') is-invalid @enderror"
                        name="email" value="{{ old('email') }}"
                        required placeholder="{{ __('auth.Email Address') }}">
                    <label for="email">{{ __('auth.Email Address') }}</label>
                    @error('email')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
            </div>

            {{-- Password --}}
            <div class="mb-4">
                <div class="form-floating">
                    <input id="password" type="password"
                        class="form-control @error('password') is-invalid @enderror"
                        name="password" required
                        placeholder="{{ __('auth.Password') }}">
                    <label for="password">{{ __('auth.Password') }}</label>
                    @error('password')
                        <div class="invalid-feedback">{{ $message }}</div>
                    @enderror
                </div>
                <div class="mt-1 text-muted small">
                    <i class="ri-information-line"></i> {{ __('auth.Password must be at least 8 characters') }}
                </div>
            </div>

            {{-- Confirm Password --}}
            <div class="mb-4">
                <div class="form-floating">
                    <input id="password-confirm" type="password"
                        class="form-control"
                        name="password_confirmation" required
                        placeholder="{{ __('auth.Confirm Password') }}">
                    <label for="password-confirm">{{ __('auth.Confirm Password') }}</label>
                </div>
            </div>

            {{-- Terms --}}
            <div class="form-check mb-4">
                <input class="form-check-input" type="checkbox" name="terms" id="terms" required>
                <label class="form-check-label" for="terms">
                    {{ __('auth.I agree to the') }} <a href="#" class="text-primary">{{ __('auth.Terms and Conditions') }}</a>
                </label>
            </div>

            {{-- Submit --}}
            <div class="mb-4">
                <button type="submit" class="btn btn-primary w-100 waves-effect waves-light hover-shadow">
                    <i class="ri-user-add-line align-middle me-1"></i> {{ __('auth.Register') }}
                </button>
            </div>
        </form>
    </div>
</div>
@endsection
