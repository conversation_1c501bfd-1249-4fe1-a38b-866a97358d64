<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\View;

class SharedViewController extends Controller
{
    /**
     * Affiche une vue partagée en utilisant le thème approprié pour l'utilisateur
     *
     * @param string $view
     * @param array $data
     * @return \Illuminate\View\View
     */
    public function show($view, $data = [])
    {
        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = user_theme();
        
        // Ajouter le thème aux données de la vue
        $data['userTheme'] = $theme;
        
        // Retourner la vue avec les données
        return view($view, $data);
    }
}
