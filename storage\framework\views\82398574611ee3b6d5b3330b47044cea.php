

<?php $__env->startSection('contenu'); ?>

<div class="container-fluid">

    <?php if(session('message')): ?>
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            <?php echo e(session('message')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
        </div>
    <?php endif; ?>

    <div class="card mt-3">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h4 class="card-title mb-0"><?php echo e(__('message.Staff List')); ?></h4>
            <a href="/ajoutpersonnel" class="btn btn-success"><?php echo e(__('message.Add_personnel')); ?></a>
        </div>
        <div class="card-body">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th><?php echo e(__('message.ID')); ?></th>
                        <th><?php echo e(__('message.User')); ?></th>
                        <th><?php echo e(__('message.Email_list')); ?></th>
                        <th><?php echo e(__('message.Position')); ?></th>
                        <th><?php echo e(__('message.Actions_personnel')); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__currentLoopData = $data; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($user->id); ?></td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="avatar avatar-sm me-2" style="background-color: #4ECDC4; color: white;">
                                        <span class="avatar-initials"><?php echo e(strtoupper(substr($user->name, 0, 1))); ?></span>
                                    </div>
                                    <div>
                                        <div class="fw-semibold"><?php echo e($user->name); ?></div>
                                        <small class="text-muted"><?php echo e(ucfirst($user->role)); ?></small>
                                    </div>
                                </div>
                            </td>
                            <td><?php echo e($user->email); ?></td>
                            <td>
                                <span class="badge bg-info-subtle text-info">
                                    <?php echo e($user->poste ?? 'N/A'); ?>

                                </span>
                            </td>
                            <td>
                                <div class="d-flex gap-1">
                                    <a href="/modifier-personnel/<?php echo e($user->id); ?>" class="btn btn-warning btn-sm">
                                        <i class="ri-edit-line me-1"></i><?php echo e(__('message.Edit_personnel')); ?>

                                    </a>
                                    <a href="/supprimer-personnel/<?php echo e($user->id); ?>" onclick="return confirm('<?php echo e(__('message.Confirm delete personnel')); ?>')" class="btn btn-danger btn-sm">
                                        <i class="ri-delete-bin-line me-1"></i><?php echo e(__('message.Delete_personnel')); ?>

                                    </a>
                                </div>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php echo $__env->make($userTheme ?? 'theme', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\task\resources\views/liste-personnel.blade.php ENDPATH**/ ?>