@extends($userTheme ?? 'theme')

@section('contenu')

                    <div class="row">
                        <div class="col-12">
                            <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
                                <h4 class="mb-sm-0">{{ __('message.Contact List') }}</h4>

                                <div class="page-title-right">
                                    <ol class="breadcrumb m-0">
                                        <li class="breadcrumb-item"><a href="javascript: void(0);">Contact</a></li>
                                        <li class="breadcrumb-item active">{{ __('message.Contact List') }}</li>
                                    </ol>
                                </div>

                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-xl-12">
                            <div class="card">
                                <div class="card-header align-items-center d-flex">
                                    <h4 class="card-title mb-0 flex-grow-1">{{ __('message.Contact List') }}</h4>
                                    <div class="flex-shrink-0">

                                    </div>
                                </div>

                                <div class="card-body">
                                <div class="live-preview">
                                        <div class="table-responsive">
                                            @if(session('message'))


                                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                                        {{session('message')}}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                                        </div>
                                        @endif

                                            <table class="table align-middle table-nowrap mb-0">
                                                <thead>
                                                    <tr>
                                                        <th scope="col">{{ __('message.ID') }}</th>
                                                        <th scope="col">{{ __('message.Name_list') }}</th>
                                                        <th scope="col">{{ __('message.Email_list') }}</th>
                                                        <th scope="col">{{ __('message.Subject') }}</th>
                                                        <th scope="col">{{ __('message.Actions_personnel') }}</th>

                                                    </tr>
                                                </thead>
                                                <tbody>
    @foreach($contacts as $contact)
    <tr>
        <td>{{ $contact->id }}</td>
        <td>{{ $contact->nom }}</td>
        <td>{{ $contact->email }}</td>
        <td>{{ $contact->sujet }}</td>
        <td>
            <a href="{{ url('/afficher-message/'.$contact->id) }}" class="btn btn-sm btn-primary">{{ __('message.View_contact') }}</a>

            <a href="/suppContact/{{ $contact->id }}" class="btn btn-sm btn-danger">{{ __('message.Delete_admin') }}</a>
        </td>
    </tr>
    @endforeach
</tbody>
                                            </table>
                                        </div>
                                    </div>

                                    <div class="d-none code-view">

                                    </div>
                                </div><!-- end card-body -->
                            </div><!-- end card -->
                        </div>
                        <!-- end col -->


                    </div>
                    <!-- end row -->


@endsection