function loadTranslations() {
    fetch('/js/lang')
        .then(response => response.json())
        .then(data => {
            document.getElementById('menu-dashboard').innerText = data.messages.dashboard;
            document.getElementById('menu-administrator').innerText = data.messages.administrator;
            document.getElementById('menu-admin-list').innerText = data.messages.admin_list;
            document.getElementById('menu-add-admin').innerText = data.messages.add_admin; document.getElementById('table-id').innerText = data.messages.id;
            document.getElementById('table-name').innerText = data.messages.name;
            document.getElementById('table-email').innerText = data.messages.email;
            document.getElementById('table-password').innerText = data.messages.password;
            document.getElementById('table-action').innerText = data.messages.action;
                    

            document.querySelectorAll('.btn-edit').forEach(btn => btn.innerText = data.messages.edit);
            document.querySelectorAll('.btn-delete').forEach(btn => btn.innerText = data.messages.delete);
        });
}