@extends($userTheme ?? 'admintheme')

@section('contenu')
<!-- Inclure Animate.css pour les animations -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css" />
<div class="page-content">
    <div class="container-fluid">

        <!-- start page title -->
        <div class="row">
            <div class="col-12">
                <div class="page-title-box d-sm-flex align-items-center justify-content-between bg-galaxy-transparent">
                    <h4 class="mb-sm-0">
                        @if(isset($projetId))
                            <div class="animate__animated animate__fadeInDown d-flex align-items-center">
                                <i class="ri-dashboard-line text-primary me-2"></i>
                                <span>Kanban Board</span>
                                @if($selectedProjet)
                                    <span class="mx-2">-</span>
                                    <span class="text-primary">{{ $selectedProjet->project_name }}</span>
                                @endif

                                @if($allProjets->count() > 1)
                                <!-- Sélecteur compact de projet -->
                                <div class="dropdown ms-3">
                                    <button class="btn btn-sm btn-soft-info dropdown-toggle" type="button" id="compactProjectSelector" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="ri-exchange-line me-1"></i> Changer
                                    </button>
                                    <ul class="dropdown-menu" aria-labelledby="compactProjectSelector">
                                        @foreach($allProjets as $projet)
                                        <li>
                                            <a class="dropdown-item {{ $projet->id == $projetId ? 'active' : '' }}" href="{{ route('entreprise.dashboard', ['projet_id' => $projet->id]) }}">
                                                {{ $projet->project_name }}
                                                @if($projet->id == $projetId)
                                                    <i class="ri-check-line ms-1"></i>
                                                @endif
                                            </a>
                                        </li>
                                        @endforeach
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item" href="{{ route('entreprise.dashboard') }}">
                                                <i class="ri-apps-line me-1"></i> Voir tous les projets
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                                @endif

                                <a href="{{ route('projects.list') }}" class="btn btn-sm btn-soft-primary ms-2">
                                    <i class="ri-arrow-left-line me-1"></i> Retour à la liste
                                </a>
                            </div>
                        @else
                            <div class="d-flex align-items-center">
                                <span>Tableau de bord Entreprise</span>
                                @if($allProjets->count() > 0)
                                    <span class="badge bg-info ms-2">{{ $allProjets->count() }} projet(s)</span>
                                @endif
                            </div>
                        @endif
                    </h4>

                    <div class="page-title-right">
                        <div class="d-flex align-items-center">
                            <!-- Bouton Créer un projet pour les entreprises -->
                            @if(Auth::user()->role === 'entreprise')
                            <a href="/projet" class="btn btn-success me-3 animate__animated animate__pulse animate__infinite animate__slower">
                                <i class="ri-add-circle-line me-1"></i> Créer un projet
                            </a>
                            @endif

                            <ol class="breadcrumb m-0">
                                <li class="breadcrumb-item"><a href="javascript: void(0);">Projets</a></li>
                                <li class="breadcrumb-item active">Tableau de bord</li>
                            </ol>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- end page title -->

        <!-- Sélecteur de projet pour les entreprises avec plusieurs projets -->
        @if($allProjets->count() > 1 && !isset($projetId))
        <div class="row mb-4">
            <div class="col-12">
                <div class="card animate__animated animate__fadeIn">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-1">
                                    <i class="ri-folder-line text-primary me-2"></i>Sélectionner un projet
                                </h5>
                                <p class="text-muted mb-0">Vous avez {{ $allProjets->count() }} projets. Choisissez un projet pour voir son tableau Kanban.</p>
                            </div>
                            <div class="flex-shrink-0">
                                <div class="dropdown">
                                    <button class="btn btn-primary dropdown-toggle animate__animated animate__pulse animate__infinite animate__slower" type="button" id="projectSelector" data-bs-toggle="dropdown" aria-expanded="false">
                                        <i class="ri-arrow-down-s-line me-1"></i> Choisir un projet
                                    </button>
                                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="projectSelector">
                                        <li><h6 class="dropdown-header">Mes Projets ({{ $allProjets->count() }})</h6></li>
                                        <li><hr class="dropdown-divider"></li>
                                        @foreach($allProjets as $projet)
                                        <li>
                                            <a class="dropdown-item d-flex align-items-center" href="{{ route('entreprise.dashboard', ['projet_id' => $projet->id]) }}">
                                                <div class="flex-grow-1">
                                                    <div class="fw-medium">{{ $projet->project_name }}</div>
                                                    <small class="text-muted">{{ $projet->taches->count() }} tâche(s)</small>
                                                </div>
                                                <div class="flex-shrink-0 ms-2">
                                                    @if($projet->taches->count() > 0)
                                                        @php
                                                            $progression = round(($projet->taches->where('statut', 'done')->count() / $projet->taches->count()) * 100);
                                                        @endphp
                                                        <span class="badge bg-success">{{ $progression }}%</span>
                                                    @else
                                                        <span class="badge bg-secondary">Nouveau</span>
                                                    @endif
                                                </div>
                                            </a>
                                        </li>
                                        @endforeach
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item text-center text-primary" href="{{ route('projects.list') }}">
                                                <i class="ri-list-check me-1"></i> Voir tous les projets
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Email notification card - visible uniquement pour les entreprises -->
        @if(!isset($projetId) && Auth::user()->role === 'entreprise')
        <div class="row mb-4">
            <div class="col-12">
                <div class="card animate__animated animate__fadeIn">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <h5 class="card-title mb-1">Notification par Email</h5>
                                <p class="text-muted mb-0">Envoyez un email de rejoindre à vos clients</p>
                            </div>
                            <div class="flex-shrink-0">
                                <button type="button" class="btn btn-primary animate__animated animate__pulse animate__infinite animate__slower" data-bs-toggle="modal" data-bs-target="#emailModal">
                                    <i class="ri-mail-send-line align-middle me-1"></i> Envoyer un Email
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

        <!-- Projets et leurs tâches -->
        @if($allProjets->count() > 1 && !isset($projetId))
            <!-- Message d'information pour sélection de projet -->
            <div class="row">
                <div class="col-12">
                    <div class="card border-0 shadow-lg animate__animated animate__fadeIn">
                        <div class="card-body text-center py-5">
                            <div class="mb-4">
                                <i class="ri-folder-open-line display-4 text-primary"></i>
                            </div>
                            <h4 class="mb-3">Sélectionnez un projet pour voir son tableau Kanban</h4>
                            <p class="text-muted mb-4">
                                Vous avez {{ $allProjets->count() }} projets actifs. Utilisez le sélecteur ci-dessus pour choisir le projet dont vous souhaitez voir le tableau Kanban détaillé.
                            </p>
                            <div class="row justify-content-center">
                                @foreach($allProjets->take(3) as $projet)
                                <div class="col-md-4 mb-3">
                                    <div class="card border">
                                        <div class="card-body">
                                            <h6 class="card-title">{{ $projet->project_name }}</h6>
                                            <p class="card-text small text-muted">{{ $projet->taches->count() }} tâche(s)</p>
                                            <a href="{{ route('entreprise.dashboard', ['projet_id' => $projet->id]) }}" class="btn btn-primary btn-sm">
                                                <i class="ri-kanban-view me-1"></i> Voir Kanban
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                @endforeach
                            </div>
                            @if($allProjets->count() > 3)
                            <p class="text-muted mt-3">
                                <small>Et {{ $allProjets->count() - 3 }} autre(s) projet(s) disponible(s) dans le sélecteur</small>
                            </p>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @else
            @foreach($projets as $projet)
        <div class="card mb-4 shadow-lg border-0 animate__animated animate__fadeIn">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <span class="animate__animated animate__fadeIn">{{ $projet->project_name }}</span>
                    @if(isset($projetId) && $projetId == $projet->id)
                    <span class="badge bg-primary ms-2 animate__animated animate__fadeIn animate__delay-1s">Projet sélectionné</span>
                    @endif
                </h5>
                <div>
                    <a href="{{ route('projects.list') }}" class="btn btn-sm btn-soft-secondary me-1 animate__animated animate__fadeIn">
                        <i class="ri-list-check me-1"></i> Liste
                    </a>
                    <a href="{{ route('projet-detail', ['id' => $projet->id]) }}" class="btn btn-sm btn-primary animate__animated animate__fadeIn">
                        <i class="ri-eye-fill me-1"></i> Voir détails
                    </a>
                </div>
            </div>
            <div class="card-body">
                @if($projet->project_description)
                <div class="mb-3">
                    <h6>Description:</h6>
                    <p>{{ $projet->project_description }}</p>
                </div>
                @endif

                <h6 class="mb-4 fw-semibold">Tâches associées - Kanban Board:</h6>
                @if($projet->taches && $projet->taches->count() > 0)
                    <div class="kanban-board">
                        <div class="row g-3">
                            <!-- Colonne À faire -->
                            <div class="col-md-3">
                                <div class="card border-0 shadow-sm kanban-column animate__animated animate__fadeIn">
                                    <div class="card-header bg-secondary text-white">
                                        <h5 class="mb-0">
                                            <i class="ri-list-check-2 me-1"></i> À faire
                                            <span class="badge bg-light text-dark ms-1">
                                                {{ $projet->taches->where('statut', 'to_do')->count() }}
                                            </span>
                                        </h5>
                                    </div>
                                    <div class="card-body p-2 kanban-tasks-container" id="todo-tasks-{{ $projet->id }}">
                                        @foreach($projet->taches->where('statut', 'to_do') as $tache)
                                        <div class="card mb-2 kanban-task animate__animated animate__fadeInUp" data-task-id="{{ $tache->id }}">
                                            <div class="card-body p-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h6 class="card-title mb-0 text-truncate">{{ $tache->nom }}</h6>
                                                    <span class="badge bg-secondary">À faire</span>
                                                </div>
                                                <p class="card-text small text-muted mb-2">{{ Str::limit($tache->description, 50, '...') }}</p>
                                                <div class="progress mb-2" style="height: 8px;">
                                                    <div class="progress-bar bg-secondary progress-bar-striped progress-bar-animated" role="progressbar"
                                                        style="width: {{ $tache->avancement }}%;"
                                                        aria-valuenow="{{ $tache->avancement }}" aria-valuemin="0" aria-valuemax="100">
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        <i class="ri-calendar-line me-1"></i>
                                                        {{ \Carbon\Carbon::parse($tache->deadline)->format('d/m/Y') }}
                                                    </small>
                                                    <div class="avatar-group">
                                                        @if($tache->personnel)
                                                            <div class="avatar-xs" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ $tache->personnel->name }}">
                                                                <div class="avatar-title rounded-circle bg-primary">
                                                                    {{ substr($tache->personnel->name, 0, 1) }}
                                                                </div>
                                                            </div>
                                                        @else
                                                            <div class="avatar-xs" data-bs-toggle="tooltip" data-bs-placement="top" title="Non assigné">
                                                                <div class="avatar-title rounded-circle bg-light text-muted">
                                                                    <i class="ri-user-line"></i>
                                                                </div>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                                <div class="mt-2 text-end">
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <a href="{{ route('tache-detail', ['id' => $tache->id]) }}" class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" title="Voir détails">
                                                            <i class="ri-eye-fill"></i>
                                                        </a>
                                                        @if(Auth::user()->role !== 'client')
                                                        <a href="/modifier-tache/{{ $tache->id }}" class="btn btn-sm btn-outline-warning" data-bs-toggle="tooltip" title="Modifier">
                                                            <i class="ri-edit-2-line"></i>
                                                        </a>
                                                        <a href="/supprimer-tache/{{ $tache->id }}" class="btn btn-sm btn-outline-danger" data-bs-toggle="tooltip" title="Supprimer"
                                                           onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette tâche?')">
                                                            <i class="ri-delete-bin-5-line"></i>
                                                        </a>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                            <!-- Colonne En cours -->
                            <div class="col-md-3">
                                <div class="card border-0 shadow-sm kanban-column animate__animated animate__fadeIn animate__delay-1s">
                                    <div class="card-header bg-warning text-dark">
                                        <h5 class="mb-0">
                                            <i class="ri-time-line me-1"></i> En cours
                                            <span class="badge bg-light text-dark ms-1">
                                                {{ $projet->taches->where('statut', 'doing')->count() }}
                                            </span>
                                        </h5>
                                    </div>
                                    <div class="card-body p-2 kanban-tasks-container" id="doing-tasks-{{ $projet->id }}">
                                        @foreach($projet->taches->where('statut', 'doing') as $tache)
                                        <div class="card mb-2 kanban-task animate__animated animate__fadeInUp" data-task-id="{{ $tache->id }}">
                                            <div class="card-body p-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h6 class="card-title mb-0 text-truncate">{{ $tache->nom }}</h6>
                                                    <span class="badge bg-warning">En cours</span>
                                                </div>
                                                <p class="card-text small text-muted mb-2">{{ Str::limit($tache->description, 50, '...') }}</p>
                                                <div class="progress mb-2" style="height: 8px;">
                                                    <div class="progress-bar bg-warning progress-bar-striped progress-bar-animated" role="progressbar"
                                                        style="width: {{ $tache->avancement }}%;"
                                                        aria-valuenow="{{ $tache->avancement }}" aria-valuemin="0" aria-valuemax="100">
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        <i class="ri-calendar-line me-1"></i>
                                                        {{ \Carbon\Carbon::parse($tache->deadline)->format('d/m/Y') }}
                                                    </small>
                                                    <div class="avatar-group">
                                                        @if($tache->personnel)
                                                            <div class="avatar-xs" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ $tache->personnel->name }}">
                                                                <div class="avatar-title rounded-circle bg-primary">
                                                                    {{ substr($tache->personnel->name, 0, 1) }}
                                                                </div>
                                                            </div>
                                                        @else
                                                            <div class="avatar-xs" data-bs-toggle="tooltip" data-bs-placement="top" title="Non assigné">
                                                                <div class="avatar-title rounded-circle bg-light text-muted">
                                                                    <i class="ri-user-line"></i>
                                                                </div>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                                <div class="mt-2 text-end">
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <a href="{{ route('tache-detail', ['id' => $tache->id]) }}" class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" title="Voir détails">
                                                            <i class="ri-eye-fill"></i>
                                                        </a>
                                                        @if(Auth::user()->role !== 'client')
                                                        <a href="/modifier-tache/{{ $tache->id }}" class="btn btn-sm btn-outline-warning" data-bs-toggle="tooltip" title="Modifier">
                                                            <i class="ri-edit-2-line"></i>
                                                        </a>
                                                        <a href="/supprimer-tache/{{ $tache->id }}" class="btn btn-sm btn-outline-danger" data-bs-toggle="tooltip" title="Supprimer"
                                                           onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette tâche?')">
                                                            <i class="ri-delete-bin-5-line"></i>
                                                        </a>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                            <!-- Colonne Bug -->
                            <div class="col-md-3">
                                <div class="card border-0 shadow-sm kanban-column animate__animated animate__fadeIn animate__delay-2s">
                                    <div class="card-header bg-danger text-white">
                                        <h5 class="mb-0">
                                            <i class="ri-error-warning-line me-1"></i> Bug
                                            <span class="badge bg-light text-dark ms-1">
                                                {{ $projet->taches->where('statut', 'bug')->count() }}
                                            </span>
                                        </h5>
                                    </div>
                                    <div class="card-body p-2 kanban-tasks-container" id="bug-tasks-{{ $projet->id }}">
                                        @foreach($projet->taches->where('statut', 'bug') as $tache)
                                        <div class="card mb-2 kanban-task animate__animated animate__fadeInUp" data-task-id="{{ $tache->id }}">
                                            <div class="card-body p-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h6 class="card-title mb-0 text-truncate">{{ $tache->nom }}</h6>
                                                    <span class="badge bg-danger">Bug</span>
                                                </div>
                                                <p class="card-text small text-muted mb-2">{{ Str::limit($tache->description, 50, '...') }}</p>
                                                <div class="progress mb-2" style="height: 8px;">
                                                    <div class="progress-bar bg-danger progress-bar-striped progress-bar-animated" role="progressbar"
                                                        style="width: {{ $tache->avancement }}%;"
                                                        aria-valuenow="{{ $tache->avancement }}" aria-valuemin="0" aria-valuemax="100">
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        <i class="ri-calendar-line me-1"></i>
                                                        {{ \Carbon\Carbon::parse($tache->deadline)->format('d/m/Y') }}
                                                    </small>
                                                    <div class="avatar-group">
                                                        @if($tache->personnel)
                                                            <div class="avatar-xs" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ $tache->personnel->name }}">
                                                                <div class="avatar-title rounded-circle bg-primary">
                                                                    {{ substr($tache->personnel->name, 0, 1) }}
                                                                </div>
                                                            </div>
                                                        @else
                                                            <div class="avatar-xs" data-bs-toggle="tooltip" data-bs-placement="top" title="Non assigné">
                                                                <div class="avatar-title rounded-circle bg-light text-muted">
                                                                    <i class="ri-user-line"></i>
                                                                </div>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                                <div class="mt-2 text-end">
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <a href="{{ route('tache-detail', ['id' => $tache->id]) }}" class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" title="Voir détails">
                                                            <i class="ri-eye-fill"></i>
                                                        </a>
                                                        @if(Auth::user()->role !== 'client')
                                                        <a href="/modifier-tache/{{ $tache->id }}" class="btn btn-sm btn-outline-warning" data-bs-toggle="tooltip" title="Modifier">
                                                            <i class="ri-edit-2-line"></i>
                                                        </a>
                                                        <a href="/supprimer-tache/{{ $tache->id }}" class="btn btn-sm btn-outline-danger" data-bs-toggle="tooltip" title="Supprimer"
                                                           onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette tâche?')">
                                                            <i class="ri-delete-bin-5-line"></i>
                                                        </a>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>

                            <!-- Colonne Terminé -->
                            <div class="col-md-3">
                                <div class="card border-0 shadow-sm kanban-column animate__animated animate__fadeIn animate__delay-3s">
                                    <div class="card-header bg-success text-white">
                                        <h5 class="mb-0">
                                            <i class="ri-checkbox-circle-line me-1"></i> Terminé
                                            <span class="badge bg-light text-dark ms-1">
                                                {{ $projet->taches->where('statut', 'done')->count() }}
                                            </span>
                                        </h5>
                                    </div>
                                    <div class="card-body p-2 kanban-tasks-container" id="done-tasks-{{ $projet->id }}">
                                        @foreach($projet->taches->where('statut', 'done') as $tache)
                                        <div class="card mb-2 kanban-task animate__animated animate__fadeInUp" data-task-id="{{ $tache->id }}">
                                            <div class="card-body p-3">
                                                <div class="d-flex justify-content-between align-items-center mb-2">
                                                    <h6 class="card-title mb-0 text-truncate">{{ $tache->nom }}</h6>
                                                    <span class="badge bg-success">Terminé</span>
                                                </div>
                                                <p class="card-text small text-muted mb-2">{{ Str::limit($tache->description, 50, '...') }}</p>
                                                <div class="progress mb-2" style="height: 8px;">
                                                    <div class="progress-bar bg-success progress-bar-striped" role="progressbar"
                                                        style="width: {{ $tache->avancement }}%;"
                                                        aria-valuenow="{{ $tache->avancement }}" aria-valuemin="0" aria-valuemax="100">
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-between align-items-center">
                                                    <small class="text-muted">
                                                        <i class="ri-calendar-line me-1"></i>
                                                        {{ \Carbon\Carbon::parse($tache->deadline)->format('d/m/Y') }}
                                                    </small>
                                                    <div class="avatar-group">
                                                        @if($tache->personnel)
                                                            <div class="avatar-xs" data-bs-toggle="tooltip" data-bs-placement="top" title="{{ $tache->personnel->name }}">
                                                                <div class="avatar-title rounded-circle bg-primary">
                                                                    {{ substr($tache->personnel->name, 0, 1) }}
                                                                </div>
                                                            </div>
                                                        @else
                                                            <div class="avatar-xs" data-bs-toggle="tooltip" data-bs-placement="top" title="Non assigné">
                                                                <div class="avatar-title rounded-circle bg-light text-muted">
                                                                    <i class="ri-user-line"></i>
                                                                </div>
                                                            </div>
                                                        @endif
                                                    </div>
                                                </div>
                                                <div class="mt-2 text-end">
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <a href="{{ route('tache-detail', ['id' => $tache->id]) }}" class="btn btn-sm btn-outline-info" data-bs-toggle="tooltip" title="Voir détails">
                                                            <i class="ri-eye-fill"></i>
                                                        </a>
                                                        @if(Auth::user()->role !== 'client')
                                                        <a href="/modifier-tache/{{ $tache->id }}" class="btn btn-sm btn-outline-warning" data-bs-toggle="tooltip" title="Modifier">
                                                            <i class="ri-edit-2-line"></i>
                                                        </a>
                                                        <a href="/supprimer-tache/{{ $tache->id }}" class="btn btn-sm btn-outline-danger" data-bs-toggle="tooltip" title="Supprimer"
                                                           onclick="return confirm('Êtes-vous sûr de vouloir supprimer cette tâche?')">
                                                            <i class="ri-delete-bin-5-line"></i>
                                                        </a>
                                                        @endif
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endforeach
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="alert alert-info animate__animated animate__fadeIn">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <i class="ri-information-line me-2"></i>
                                <strong>Projet créé avec succès !</strong><br>
                                <small class="text-muted">Aucune tâche associée à ce projet pour le moment. Commencez par ajouter votre première tâche pour voir le tableau Kanban en action.</small>
                            </div>
                            @if(Auth::user()->role !== 'client')
                            <div class="flex-shrink-0">
                                <a href="/ajout-tache?projet_id={{ $projet->id }}" class="btn btn-primary btn-sm">
                                    <i class="ri-add-line me-1"></i> Première tâche
                                </a>
                            </div>
                            @endif
                        </div>
                    </div>
                @endif

                @if(Auth::user()->role !== 'client')
                <div class="mt-3">
                    <a href="/ajout-tache?projet_id={{ $projet->id }}" class="btn btn-primary">
                        <i class="ri-add-line align-bottom me-1"></i> Ajouter une tâche
                    </a>
                </div>
                @endif
            </div>
        </div>
        @endforeach
        @endif

        <!-- Section pour les entreprises sans projets -->
        @if($allProjets->count() == 0 && Auth::user()->role === 'entreprise')
        <div class="row">
            <div class="col-12">
                <div class="card border-0 shadow-lg animate__animated animate__fadeIn">
                    <div class="card-body text-center py-5">
                        <div class="mb-4">
                            <i class="ri-folder-add-line display-1 text-primary animate__animated animate__bounce animate__infinite animate__slower"></i>
                        </div>
                        <h3 class="mb-3">Bienvenue dans votre espace entreprise !</h3>
                        <p class="text-muted mb-4 lead">
                            Vous n'avez pas encore de projet. Créez votre premier projet pour commencer à organiser vos tâches et collaborer avec votre équipe.
                        </p>

                        <div class="row justify-content-center mb-4">
                            <div class="col-md-8">
                                <div class="row">
                                    <div class="col-md-4 mb-3">
                                        <div class="text-center">
                                            <div class="avatar-lg bg-primary-subtle text-primary rounded-circle mx-auto mb-3">
                                                <i class="ri-folder-line fs-22"></i>
                                            </div>
                                            <h6>Créer des projets</h6>
                                            <p class="text-muted small">Organisez votre travail en projets distincts</p>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="text-center">
                                            <div class="avatar-lg bg-success-subtle text-success rounded-circle mx-auto mb-3">
                                                <i class="ri-task-line fs-22"></i>
                                            </div>
                                            <h6>Gérer les tâches</h6>
                                            <p class="text-muted small">Suivez l'avancement avec le tableau Kanban</p>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-3">
                                        <div class="text-center">
                                            <div class="avatar-lg bg-info-subtle text-info rounded-circle mx-auto mb-3">
                                                <i class="ri-team-line fs-22"></i>
                                            </div>
                                            <h6>Collaborer</h6>
                                            <p class="text-muted small">Invitez votre équipe et vos clients</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-center gap-3">
                            <a href="/projet" class="btn btn-primary btn-lg animate__animated animate__pulse animate__infinite animate__slower">
                                <i class="ri-add-circle-line me-2"></i> Créer mon premier projet
                            </a>
                            <a href="/ajoutpersonnel" class="btn btn-outline-secondary btn-lg">
                                <i class="ri-user-add-line me-2"></i> Ajouter des membres
                            </a>
                        </div>

                        <div class="mt-4">
                            <small class="text-muted">
                                <i class="ri-lightbulb-line me-1"></i>
                                Astuce : Commencez par créer un projet, puis ajoutez des tâches pour voir le tableau Kanban en action !
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        @endif

    </div>
</div>

<!-- Modal pour l'envoi d'email -->
<div class="modal fade" id="emailModal" tabindex="-1" aria-labelledby="emailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="emailModalLabel">Ajouter un nouveau client</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="/create-client" method="POST" id="emailForm">
                @csrf
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="name" class="form-label">Nom complet du client</label>
                        <input type="text" class="form-control" id="name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="email" class="form-label">Adresse Email</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    <div class="mb-3">
                        <label for="password" class="form-label">Mot de passe</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                    </div>
                    <div class="alert alert-info">
                        <div class="d-flex">
                            <div class="flex-shrink-0">
                                <i class="ri-information-line fs-18 align-middle me-2"></i>
                            </div>
                            <div class="flex-grow-1">
                                <p class="mb-0">Une invitation sera envoyée au client par email. Le compte ne sera créé que lorsque le client cliquera sur le bouton de confirmation dans l'email.</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-light" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary" id="sendEmailBtn">
                        <i class="ri-mail-send-line align-middle me-1"></i> Envoyer l'invitation
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Toast pour les notifications -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 1080">
    <div id="emailToast" class="toast hide" role="alert" aria-live="assertive" aria-atomic="true">
        <div class="toast-header bg-success text-white">
            <i class="ri-check-line me-2"></i>
            <strong class="me-auto">Succès</strong>
            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            Invitation envoyée au client avec succès!
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const emailForm = document.getElementById('emailForm');
        const emailToast = document.getElementById('emailToast');

        if (emailForm) {
            emailForm.addEventListener('submit', function(e) {
                e.preventDefault();

                // Récupérer les données du formulaire
                const formData = new FormData(emailForm);

                // Envoyer la requête AJAX
                fetch('/create-client', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Erreur lors de la création du client');
                    }
                    return response.text();
                })
                .then(data => {
                    // Afficher le toast de succès
                    const toast = new bootstrap.Toast(emailToast);
                    toast.show();

                    // Fermer le modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('emailModal'));
                    modal.hide();

                    // Réinitialiser le formulaire
                    emailForm.reset();
                })
                .catch(error => {
                    console.error('Erreur:', error);
                    alert('Une erreur est survenue lors de la création du client. Veuillez réessayer.');
                });
            });
        }
    });

    // Initialiser les tooltips Bootstrap
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // Animation des cartes Kanban au survol
    document.querySelectorAll('.kanban-task').forEach(function(card) {
        card.addEventListener('mouseenter', function() {
            this.classList.add('animate__pulse');
        });
        card.addEventListener('mouseleave', function() {
            this.classList.remove('animate__pulse');
        });
    });

    // Animation des colonnes au chargement de la page
    document.addEventListener('DOMContentLoaded', function() {
        // Animer les colonnes une par une avec un délai
        document.querySelectorAll('.kanban-column').forEach(function(column, index) {
            setTimeout(function() {
                column.classList.add('animate__fadeIn');
            }, index * 200);
        });

        // Animer les tâches avec un effet cascade
        document.querySelectorAll('.kanban-tasks-container').forEach(function(container) {
            Array.from(container.children).forEach(function(task, index) {
                setTimeout(function() {
                    task.classList.add('animate__fadeInUp');
                }, 500 + (index * 100));
            });
        });
    });
</script>

<style>
    /* Styles pour le Kanban Board */
    .kanban-board {
        overflow-x: auto;
        padding-bottom: 1rem;
    }

    .kanban-column {
        min-height: 300px;
        transition: all 0.3s ease;
        border-radius: 0.5rem;
    }

    .kanban-column:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
        transform: translateY(-5px);
    }

    .kanban-column .card-header {
        border-top-left-radius: 0.5rem;
        border-top-right-radius: 0.5rem;
    }

    .kanban-tasks-container {
        min-height: 200px;
        max-height: 500px;
        overflow-y: auto;
    }

    .kanban-task {
        cursor: pointer;
        transition: all 0.3s ease;
        border: none;
        border-radius: 0.5rem;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    }

    .kanban-task:hover {
        transform: translateY(-3px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
        z-index: 1;
    }

    /* Styles spécifiques pour chaque colonne */
    #todo-tasks-{{ $projet->id ?? 0 }} .kanban-task {
        border-left: 4px solid #6c757d;
    }

    #doing-tasks-{{ $projet->id ?? 0 }} .kanban-task {
        border-left: 4px solid #ffc107;
    }

    #bug-tasks-{{ $projet->id ?? 0 }} .kanban-task {
        border-left: 4px solid #dc3545;
    }

    #done-tasks-{{ $projet->id ?? 0 }} .kanban-task {
        border-left: 4px solid #198754;
    }

    /* Animation pour les badges */
    .badge {
        transition: all 0.3s ease;
    }

    .kanban-task:hover .badge {
        transform: scale(1.1);
    }

    /* Animation pour les boutons */
    .btn-outline-info:hover, .btn-outline-warning:hover, .btn-outline-danger:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    }

    /* Scrollbar personnalisée */
    .kanban-tasks-container::-webkit-scrollbar {
        width: 6px;
    }

    .kanban-tasks-container::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    .kanban-tasks-container::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 10px;
    }

    .kanban-tasks-container::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }

    /* Animation pour les avatars */
    .avatar-xs {
        transition: all 0.3s ease;
    }

    .avatar-xs:hover {
        transform: scale(1.2);
        z-index: 2;
    }

    /* Animation pour les progress bars */
    .progress-bar-animated {
        animation: progress-bar-stripes 1s linear infinite;
    }

    /* Styles pour le sélecteur de projet */
    .dropdown-menu {
        border: none;
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        border-radius: 0.5rem;
        min-width: 280px;
    }

    .dropdown-item {
        padding: 0.75rem 1rem;
        transition: all 0.3s ease;
    }

    .dropdown-item:hover {
        background-color: #f8f9fa;
        transform: translateX(5px);
    }

    .dropdown-item.active {
        background-color: #0d6efd;
        color: white;
    }

    .dropdown-header {
        color: #6c757d;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 0.75rem;
        letter-spacing: 0.5px;
    }

    /* Animation pour les cartes de projet */
    .card {
        transition: all 0.3s ease;
    }

    .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    }

    /* Styles pour les badges de progression */
    .badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.5rem;
    }

    /* Animation pour les boutons dropdown */
    .dropdown-toggle {
        transition: all 0.3s ease;
    }

    .dropdown-toggle:hover {
        transform: translateY(-1px);
        box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.1);
    }

    /* Responsive design pour mobile */
    @media (max-width: 768px) {
        .kanban-board .row {
            flex-wrap: nowrap;
            overflow-x: auto;
            padding-bottom: 1rem;
        }

        .kanban-board .col-md-3 {
            min-width: 280px;
            padding-right: 0.5rem;
            padding-left: 0.5rem;
        }

        .kanban-column {
            margin-bottom: 1rem;
        }

        .dropdown-menu {
            min-width: 250px;
        }

        .d-flex.align-items-center {
            flex-direction: column;
            align-items: flex-start !important;
        }

        .d-flex.align-items-center .dropdown {
            margin-top: 0.5rem;
            margin-left: 0 !important;
        }
    }
</style>
@endsection
