@extends($userTheme ?? 'theme')

@section('contenu')
<div class="container-fluid">
    <div class="row">
        <div class="col-12 d-flex justify-content-between align-items-center">
            <h4 class="mb-0">{{ __('message.Add a Task') }}</h4>
        </div>
    </div>

    <form action="/ajout-tache" method="POST">
        @csrf
        <div class="mb-3">
            <label>{{ __('message.Name_task') }}</label>
            <input type="text" name="nom" class="form-control" value="{{ old('nom') }}">
        </div>
        <div class="mb-3">
            <label>{{ __('message.Description_task') }}</label>
            <textarea name="description" class="form-control">{{ old('description') }}</textarea>
        </div>
        <div class="mb-3">
            <label>{{ __('message.Deadline_task') }}</label>
            <input type="date" name="deadline" class="form-control" value="{{ old('deadline') }}">
        </div>
        <div class="mb-3">
            <label>{{ __('message.Status_task') }}</label>
            <select name="statut" class="form-select">
                @foreach(['to_do', 'doing', 'bug', 'done'] as $s)
                    <option value="{{ $s }}" {{ old('statut') == $s ? 'selected' : '' }}>{{ ucfirst($s) }}</option>
                @endforeach
            </select>
        </div>
        <div class="mb-3">
            <label>{{ __('message.Progress_task') }} (%)</label>
            <select name="avancement" class="form-select">
                @foreach([0, 20, 40, 60, 80, 100] as $a)
                    <option value="{{ $a }}" {{ old('avancement') == $a ? 'selected' : '' }}>{{ $a }}%</option>
                @endforeach
            </select>
        </div>
        <div class="mb-3">
            <label>{{ __('message.Project_task') }}</label>
            <select name="projet_id" class="form-select">
                @foreach($projets as $projet)
                    <option value="{{ $projet->id }}" {{ old('projet_id') == $projet->id ? 'selected' : '' }}>{{ $projet->project_name }}</option>
                @endforeach
            </select>
        </div>
        <div class="mb-3">
    <label>{{ __('message.Assigned to_task') }}</label>
    <select name="personnel_id" class="form-select">
        <option value="">{{ __('message.No staff') }}</option> <!-- Option pour ne pas assigner de personnel -->
        @foreach($personnels as $personnel)
            <option value="{{ $personnel->id }}" {{ old('personnel_id') == $personnel->id ? 'selected' : '' }}>{{ $personnel->name }}</option>
        @endforeach
    </select>
</div>

        <button class="btn btn-primary">{{ __('message.Add') }}</button>
    </form>
</div>
@endsection
