<?php

namespace App\Http\Controllers;
use App\Models\contact;

use Illuminate\Http\Request;

class Landing<PERSON>ontroller extends Controller
{
    public function index()
    {
        return view('landing');
    }
    public function sendContact(Request $request)
{
    // Validation des champs du formulaire
    $request->validate([
        'nom' => 'required|string|max:255',
        'email' => 'required|email|max:255',
        'sujet' => 'required|string|max:255',
        'message' => 'required|string',
    ]);

    // Insertion dans la table contacts
    Contact::create([
        'name' => $request->nom,
        'email' => $request->email,
        'sujet' => $request->sujet,
        'message' => $request->message,
        'lire' => 0, // Non lu par défaut
    ]);

    // Redirection avec message de succès
    return redirect()->back()->with('message', 'Votre message a été envoyé avec succès !');
}



    public function list()
    {
        $contacts = Contact::latest()->get();
        return view('list-contact', compact('contacts'));
    }

    public function contactForm()
    {
        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('contact-form')->with('userTheme', $theme);
    }
}
