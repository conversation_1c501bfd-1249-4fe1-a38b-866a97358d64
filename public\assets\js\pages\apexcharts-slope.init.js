function getChartColorsArray(e){if(null!==document.getElementById(e))return e=document.getElementById(e).getAttribute("data-colors"),(e=JSON.parse(e)).map(function(e){var t=e.replace(" ","");return-1===t.indexOf(",")?getComputedStyle(document.documentElement).getPropertyValue(t)||t:2==(e=e.split(",")).length?"rgba("+getComputedStyle(document.documentElement).getPropertyValue(e[0])+","+e[1]+")":t})}var options,chart,slopeBasicColors=getChartColorsArray("basic_charts"),slopeMultiColors=(slopeBasicColors&&(options={series:[{name:"Blue",data:[{x:"Jan",y:43},{x:"Feb",y:58}]},{name:"<PERSON>",data:[{x:"Jan",y:33},{x:"Feb",y:38}]},{name:"Red",data:[{x:"Jan",y:55},{x:"Feb",y:21}]}],chart:{height:350,width:400,type:"line"},plotOptions:{line:{isSlopeChart:!0}},colors:slopeBasicColors},(chart=new ApexCharts(document.querySelector("#basic_charts"),options)).render()),getChartColorsArray("multi_charts"));slopeMultiColors&&(options={series:[{name:"Blue",data:[{x:"Category 1",y:503},{x:"Category 2",y:580},{x:"Category 3",y:135}]},{name:"Green",data:[{x:"Category 1",y:733},{x:"Category 2",y:385},{x:"Category 3",y:715}]},{name:"Orange",data:[{x:"Category 1",y:255},{x:"Category 2",y:211},{x:"Category 3",y:441}]},{name:"Red",data:[{x:"Category 1",y:428},{x:"Category 2",y:749},{x:"Category 3",y:559}]}],chart:{height:350,width:600,type:"line"},plotOptions:{line:{isSlopeChart:!0}},tooltip:{followCursor:!0,intersect:!1,shared:!0},dataLabels:{background:{enabled:!0},formatter(e,t){t=t.w.config.series[t.seriesIndex].name;return null!==e?t:""}},yaxis:{show:!0,labels:{show:!0}},xaxis:{position:"bottom"},legend:{show:!0,position:"top",horizontalAlign:"left"},stroke:{width:[2,3,4,2],dashArray:[0,0,5,2],curve:"smooth"},colors:slopeMultiColors},(chart=new ApexCharts(document.querySelector("#multi_charts"),options)).render());