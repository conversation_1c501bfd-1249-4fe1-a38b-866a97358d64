/**
 * Gestion des notifications en temps réel
 */
document.addEventListener('DOMContentLoaded', function() {
    // Sélectionner les éléments du DOM
    const notificationDropdown = document.getElementById('notificationDropdown');
    const notificationBadge = document.getElementById('userNotifContainer');

    if (!notificationDropdown || !notificationBadge) return;

    // Fonction pour mettre à jour le compteur de notifications
    function updateNotificationCount() {
        fetch('/notifications/count')
            .then(response => response.json())
            .then(data => {
                notificationBadge.textContent = data.count;
                if (data.count === 0) {
                    notificationBadge.style.display = 'none';
                } else {
                    notificationBadge.style.display = 'inline-block';
                }
            })
            .catch(error => console.error('Error:', error));
    }

    // Fonction pour charger les notifications
    function loadNotifications() {
        const notificationContent = document.querySelector('#all-noti-tab .simplebar-content');
        if (!notificationContent) return;

        fetch('/notifications/fetch')
            .then(response => response.text())
            .then(html => {
                notificationContent.innerHTML = html;
                setupMarkAsReadListeners();
            })
            .catch(error => console.error('Error:', error));
    }

    // Configurer les écouteurs d'événements pour marquer les notifications comme lues
    function setupMarkAsReadListeners() {
        const markAsReadButtons = document.querySelectorAll('.mark-as-read-btn');
        markAsReadButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault(); // Empêcher le comportement par défaut
                e.stopPropagation(); // Empêcher la propagation de l'événement
                const id = this.getAttribute('data-id');
                markAsRead(id);
            });
        });
    }

    // Fonction pour marquer une notification comme lue
    function markAsRead(id) {
        fetch(`/notifications/${id}/read`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Mettre à jour l'interface utilisateur
                const notificationItem = document.querySelector(`.mark-as-read-btn[data-id="${id}"]`).closest('.notification-item');
                if (notificationItem) {
                    // Changer l'apparence de la notification
                    notificationItem.classList.remove('bg-light-subtle');

                    // Mettre à jour la case à cocher
                    const checkbox = notificationItem.querySelector('.form-check-input');
                    if (checkbox) {
                        checkbox.checked = true;
                    }

                    // Désactiver le bouton "Marquer comme lu"
                    const button = notificationItem.querySelector(`.mark-as-read-btn[data-id="${id}"]`);
                    if (button) {
                        button.disabled = true;
                        button.classList.remove('btn-soft-success');
                        button.classList.add('btn-soft-secondary');
                        button.innerHTML = '<i class="ri-check-double-line"></i> Lu';
                    }
                }

                // Mettre à jour le compteur de notifications
                updateNotificationCount();
            }
        })
        .catch(error => console.error('Error:', error));
    }

    // Marquer toutes les notifications comme lues
    const markAllReadButton = document.querySelector('.view-all a');
    if (markAllReadButton) {
        // Ajouter un bouton "Tout marquer comme lu" à côté du bouton "Voir toutes les notifications"
        const markAllReadBtn = document.createElement('button');
        markAllReadBtn.className = 'btn btn-soft-danger waves-effect waves-light ms-2';
        markAllReadBtn.innerHTML = '<i class="ri-check-double-line align-middle"></i> Tout marquer comme lu';
        markAllReadButton.parentNode.appendChild(markAllReadBtn);

        markAllReadBtn.addEventListener('click', function() {
            fetch('/notifications/read-all', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Mettre à jour l'interface utilisateur
                    const notificationItems = document.querySelectorAll('.notification-item');
                    notificationItems.forEach(item => {
                        // Changer l'apparence de la notification
                        item.classList.remove('bg-light-subtle');

                        // Mettre à jour la case à cocher
                        const checkbox = item.querySelector('.form-check-input');
                        if (checkbox) {
                            checkbox.checked = true;
                        }

                        // Désactiver les boutons "Marquer comme lu"
                        const button = item.querySelector('.mark-as-read-btn');
                        if (button) {
                            button.disabled = true;
                            button.classList.remove('btn-soft-success');
                            button.classList.add('btn-soft-secondary');
                            button.innerHTML = '<i class="ri-check-double-line"></i> Lu';
                        }
                    });

                    // Mettre à jour le compteur de notifications
                    updateNotificationCount();
                }
            })
            .catch(error => console.error('Error:', error));
        });
    }

    // Mettre à jour les notifications lorsque le menu déroulant est ouvert
    notificationDropdown.addEventListener('show.bs.dropdown', function() {
        loadNotifications();
    });

    // Vérifier périodiquement les nouvelles notifications (toutes les 30 secondes)
    setInterval(updateNotificationCount, 30000);

    // Initialiser le compteur de notifications au chargement de la page
    updateNotificationCount();

    // Configurer les écouteurs d'événements pour les boutons "Marquer comme lu" déjà présents dans le DOM
    setupMarkAsReadListeners();
});
