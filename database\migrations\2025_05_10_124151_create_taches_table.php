<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up()
{
    Schema::create('taches', function (Blueprint $table) {
        $table->id();
        $table->string('nom');
        $table->text('description')->nullable();
        $table->date('deadline');
        $table->enum('statut', ['to_do', 'doing', 'bug', 'done'])->default('to_do');
        $table->integer('avancement')->default(0);

        // Clé étrangère vers la table projets
        $table->unsignedBigInteger('projet_id');
        $table->foreign('projet_id')->references('id')->on('projets')->onDelete('cascade');

        // Clé étrangère vers la table users (nullable)
        $table->unsignedBigInteger('personnel_id')->nullable();
        $table->foreign('personnel_id')->references('id')->on('users')->onDelete('set null');

        $table->timestamps();
    });
}

public function down()
{
    Schema::dropIfExists('taches');
}

};
