<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Helpers\AvatarHelper;
use App\Models\User;

class GenerateAvatars extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'avatars:generate {--force : Force regeneration for all users}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Generate avatars for all users based on their names';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('🎨 Génération des avatars...');

        if ($this->option('force')) {
            $this->info('🔄 Mode force activé - Régénération pour tous les utilisateurs');
            $users = User::all();
        } else {
            $users = User::whereNull('avatar')->orWhereNull('avatar_color')->get();
        }

        if ($users->isEmpty()) {
            $this->info('✅ Tous les utilisateurs ont déjà des avatars!');
            return;
        }

        $bar = $this->output->createProgressBar($users->count());
        $bar->start();

        foreach ($users as $user) {
            AvatarHelper::updateUserAvatar($user);
            $bar->advance();
        }

        $bar->finish();
        $this->newLine();
        $this->info("✅ Avatars générés pour {$users->count()} utilisateur(s)!");

        // Afficher quelques exemples
        $this->newLine();
        $this->info('📋 Exemples d\'avatars générés:');

        $examples = $users->take(5);
        foreach ($examples as $user) {
            $this->line("   👤 {$user->name} → {$user->avatar} ({$user->avatar_color})");
        }
    }
}
