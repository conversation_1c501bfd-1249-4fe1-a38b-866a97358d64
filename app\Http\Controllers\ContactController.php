<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\contact;


class ContactController extends Controller
{
    public function listContact() {
        $contacts = Contact::all(); // Récupère tous les contacts

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('list-contact', ['contacts' => $contacts])->with('userTheme', $theme); // Passe à la vue
    }

    public function getContactId($id){
        $contact = Contact::find($id);
        $contact->lire = 1;
        $contact->save();

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('afficher-message',['data'=>$contact])->with('userTheme', $theme);
    }


    public function deletecontact($id){
        $contact = contact::find(id: $id);
        $contact->delete();
        return redirect('/list-contact')->with('message',' un contact a été bien supprimé ');
    }
}