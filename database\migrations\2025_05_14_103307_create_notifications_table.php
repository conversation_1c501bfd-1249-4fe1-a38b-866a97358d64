<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // Type de notification (tache_created, tache_updated, etc.)
            $table->unsignedBigInteger('user_id'); // Utilisateur qui reçoit la notification
            $table->unsignedBigInteger('created_by')->nullable(); // Utilisateur qui a créé la notification
            $table->unsignedBigInteger('tache_id')->nullable(); // ID de la tâche concernée
            $table->text('message'); // Message de la notification
            $table->boolean('read')->default(false); // Si la notification a été lue
            $table->timestamps();

            // Clés étrangères
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('tache_id')->references('id')->on('taches')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('notifications', function (Blueprint $table) {
            $table->dropForeign(['user_id']);
            $table->dropForeign(['created_by']);
            $table->dropForeign(['tache_id']);
        });

        Schema::dropIfExists('notifications');
    }
};

