<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Http\Request;
use App\Mail\WelcomeMail;
use App\Models\User;

use App\Http\Controllers\{
    <PERSON><PERSON><PERSON>ontroller,
    ClientController,
    ClientInvitationController,
    CommentController,
    ContactController,
    DashboardController,
    EntreprisedashController,
    GoogleController,
    HomeController,
    LangueController,
    LandingController,
    NotificationController,
    PasswordController,
    PersonnelController,
    PersonnelDashController,
    ProfilController,
    ProjetController,
    SearchController,
    SharedViewController,
    StripeController,
    TacheController,
    TacheDetController
};


// Page d'accueil et authentification

Route::get('/', fn () => view('landing'));
Route::get('/login', fn () => view('auth.login'))->name('login');
Route::get('/register', fn () => view('auth.register'))->name('register');
Auth::routes();
Route::get('/home', [HomeController::class, 'index'])->name('home');


// Gestion du profil utilisateur

Route::middleware('auth')->group(function () {
    Route::get('/profil', [ProfilController::class, 'profilUser'])->name('profil');
    Route::get('/edit-profil', [ProfilController::class, 'editProfil'])->name('edit-profil');
    Route::post('/edit-profil', [ProfilController::class, 'updateProfil'])->name('edit-profil.post');
});


// Administration (Admin)

Route::middleware('auth')->group(function () {
    Route::get('/ajout-admin', function () {
        $theme = \App\Helpers\ThemeHelper::getUserTheme();
        return view('ajout-admin')->with('userTheme', $theme);
    });
    Route::post('/ajoutadmin', [AdminController::class, 'addadmin']);
    Route::get('/liste-admin', [AdminController::class, 'getAdmin']);
    Route::get('/modifier-admin/{id}', [AdminController::class, 'getAdminId']);
    Route::post('/modifAdmin', [AdminController::class, 'updateAdmin']);
    Route::get('/suppAdmin/{id}', [AdminController::class, 'deleteAdmin']);
});


// Projets

Route::middleware('auth')->group(function () {
    Route::get('/projet', function () {
        // Récupérer les membres avec le rôle 'membre' ajoutés par l'utilisateur connecté
        if (auth()->user()->role == 'entreprise') {
            $membres = \App\Models\User::where('role', 'membre')
                          ->where('entreprise_id', auth()->id())
                          ->get();
        } else {
            // Pour les admins, montrer tous les membres
            $membres = \App\Models\User::where('role', 'membre')->get();
        }

        $theme = \App\Helpers\ThemeHelper::getUserTheme();
        return view('projet', compact('membres'))->with('userTheme', $theme);
    });
    Route::post('/ajoutprojet', [ProjetController::class, 'addProjet']);
    Route::get('/liste-projet', [ProjetController::class, 'getProjet'])->name('projects.list');
    Route::get('/modifier-projet/{id}', [ProjetController::class, 'getProjetId']);
    Route::post('/modifier-projet', [ProjetController::class, 'updateProjet']);
    Route::get('/supprimer-projet/{id}', [ProjetController::class, 'deleteProjet']);
});


// Tâches

Route::get('/liste-tache', [TacheController::class, 'getTaches'])->name('taches-list');
Route::get('/ajout-tache', [TacheController::class, 'showAjoutTache']);
Route::post('/ajout-tache', [TacheController::class, 'addTache']);
Route::get('/modifier-tache/{id}', [TacheController::class, 'getTacheId']);
Route::post('/modifier-tache', [TacheController::class, 'updateTache']);
Route::get('/supprimer-tache/{id}', [TacheController::class, 'deleteTache']);
Route::resource('taches', TacheController::class);

// Détail tâches et projets
Route::get('/taches', [TacheDetController::class, 'index'])->name('taches.index');
Route::get('/tache-detail/{id}', [TacheDetController::class, 'show'])->name('tache-detail');
Route::get('/projet-detail/{id}', [TacheDetController::class, 'showProjet'])->name('projet-detail');


// Personnel

Route::get('/liste-personnel', [PersonnelController::class, 'getPersonnel']);
Route::get('/ajoutpersonnel', function () {
    $theme = \App\Helpers\ThemeHelper::getUserTheme();
    return view('ajout-personnel')->with('userTheme', $theme);
});
Route::post('/ajoutpersonnel', [PersonnelController::class, 'addPersonnel']);
Route::get('/modifier-personnel/{id}', [PersonnelController::class, 'getPersonnelId']);
Route::post('/modifier-personnel', [PersonnelController::class, 'updatePersonnel']);
Route::get('/supprimer-personnel/{id}', [PersonnelController::class, 'deletePersonnel']);


// Clients

Route::get('/liste-client', [ClientController::class, 'index'])->name('clients.index');
Route::get('/client/{id}', [ClientController::class, 'show'])->name('clients.show');
Route::put('/client/{id}', [ClientController::class, 'update'])->name('clients.update');
Route::delete('/client/{id}', [ClientController::class, 'destroy'])->name('clients.destroy');

// Invitation clients
Route::post('/create-client', [ClientInvitationController::class, 'sendInvitation']);
Route::get('/confirm-invitation/{token}', [ClientInvitationController::class, 'confirmInvitation'])->name('client.confirm');
Route::get('/set-password/{user_id}', [PasswordController::class, 'showSetPasswordForm'])->name('client.show-set-password');
Route::post('/set-password', [PasswordController::class, 'setPassword'])->name('client.set-password');


// Notifications

Route::get('/userNotif-refresh', fn () => view('userNotif'))->name('userNotif.refresh');
Route::get('/notifications/count', [NotificationController::class, 'getUnreadCount'])->name('notifications.count');
Route::get('/notifications', [NotificationController::class, 'getAll'])->name('notifications.index');
Route::get('/notifications/all', [NotificationController::class, 'getAll'])->name('notifications.all');
Route::get('/notifications/fetch', [NotificationController::class, 'fetch'])->name('notifications.fetch');
Route::post('/notifications/{id}/read', [NotificationController::class, 'markAsRead'])->name('notifications.read');
Route::post('/notifications/read-all', [NotificationController::class, 'markAllAsRead'])->name('notifications.readAll');


// Recherche

Route::get('/search', [SearchController::class, 'search'])->name('search');
Route::get('/api/search', [SearchController::class, 'searchApi'])->name('search.api');


// Contact

Route::get('/list-contact', [ContactController::class, 'listContact']);
Route::get('/afficher-message/{id}', [ContactController::class, 'getContactId']);
Route::get('/suppContact/{id}', [ContactController::class, 'deletecontact']);


// Page de contact / Landing

Route::get('/landing', [LandingController::class, 'index'])->name('landing');
Route::post('/landing', [LandingController::class, 'store'])->name('contact.store');
Route::get('/contact', [LandingController::class, 'contactForm'])->name('contact');
Route::post('/send-contact', [LandingController::class, 'sendContact'])->name('contact.send');


// Commentaires

Route::post('/comments', [CommentController::class, 'store'])->name('comments.store');
Route::post('/comments/{id}/like', [CommentController::class, 'toggleLike'])->name('comments.like');
Route::delete('/comments/{id}', [CommentController::class, 'destroy'])->name('comments.destroy');


// Google Auth

Route::get('auth/google', [GoogleController::class, 'googlepage']);
Route::get('auth/google/callback', [GoogleController::class, 'googlepage']);


// Tableau de bord

Route::get('/dashboard', [DashboardController::class, 'index'])->name('dashboard');
Route::get('/dash-entreprise', [EntreprisedashController::class, 'index'])->name('entreprise.dashboard')->middleware(['auth', 'payment.check']);
Route::get('/dash-personnel', [PersonnelDashController::class, 'index'])->name('personnel.dashboard');

// Route pour la démonstration des avatars
Route::get('/avatar-demo', function () {
    $theme = \App\Helpers\ThemeHelper::getUserTheme();
    return view('avatar-demo')->with('userTheme', $theme);
})->name('avatar.demo')->middleware('auth');

// Route pour tester les avatars dans les profils
Route::get('/test-profile-avatars', function () {
    $user = Auth::user();
    $theme = \App\Helpers\ThemeHelper::getUserTheme();

    // Simuler des données pour le test
    $projets = collect();
    $membres = collect();
    $taches = collect();

    return view('profil', compact('user', 'projets', 'membres', 'taches'))->with('userTheme', $theme);
})->name('test.profile.avatars')->middleware('auth');

// Route pour tester les images de projets
Route::get('/test-project-images', function () {
    $projects = \App\Models\projet::with('taches')->get();
    $theme = \App\Helpers\ThemeHelper::getUserTheme();

    return view('liste-projet', compact('projects'))->with('userTheme', $theme);
})->name('test.project.images')->middleware('auth');


// Paiement (Stripe)

Route::get('/checkout', [StripeController::class, 'checkout']);
Route::post('/checkout/session', [StripeController::class, 'createCheckoutSession']);
Route::get('/checkout/success', [StripeController::class, 'success'])->name('checkout.success');
Route::get('/checkout/cancel', [StripeController::class, 'cancel'])->name('checkout.cancel');


// Multilingue

Route::get('/change/{lang}', [LangueController::class, 'change'])->name('lang.change');
Route::get('/js/lang', [LangueController::class, 'getTranslations']);


// Vue partagée (exemple)

Route::get('/shared-example', function () {
    $theme = \App\Helpers\ThemeHelper::getUserTheme();
    return view('shared-example')->with('userTheme', $theme);
});

// Test registration
Route::get('/test-registration', function () {
    try {
        $user = \App\Models\User::create([
            'name' => 'Test User',
            'email' => 'test' . time() . '@example.com',
            'password' => \Illuminate\Support\Facades\Hash::make('password123'),
            'role' => 'entreprise',
        ]);

        return response()->json([
            'success' => true,
            'message' => 'User created successfully',
            'user' => $user
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error creating user: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// Test admin creation
Route::get('/test-admin-creation', function () {
    try {
        $admin = new \App\Models\User();
        $admin->name = 'Test Admin';
        $admin->email = 'admin' . time() . '@example.com';
        $admin->password = \Illuminate\Support\Facades\Hash::make('password123');
        $admin->role = 'admin';
        $admin->save();

        return response()->json([
            'success' => true,
            'message' => 'Admin created successfully',
            'admin' => $admin
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error creating admin: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});

// Test admin form submission
Route::post('/test-admin-form', function (\Illuminate\Http\Request $request) {
    try {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|min:6',
        ]);

        $admin = new \App\Models\User();
        $admin->name = $request->name;
        $admin->email = $request->email;
        $admin->password = \Illuminate\Support\Facades\Hash::make($request->password);
        $admin->role = 'admin';
        $admin->save();

        return response()->json([
            'success' => true,
            'message' => 'Admin created successfully via form',
            'admin' => $admin
        ]);
    } catch (\Exception $e) {
        return response()->json([
            'success' => false,
            'message' => 'Error creating admin via form: ' . $e->getMessage(),
            'trace' => $e->getTraceAsString()
        ]);
    }
});
