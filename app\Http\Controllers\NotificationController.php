<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Notification;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    /**
     * Récupérer les notifications pour l'utilisateur connecté
     */
    public function fetch()
    {
        $notifications = Notification::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();
        return view('partials.notifications', compact('notifications'));
    }

    /**
     * Récupérer le nombre de notifications non lues pour l'utilisateur connecté
     */
    public function getUnreadCount()
    {
        // Compter les notifications non lues pour l'utilisateur connecté
        $count = Notification::where('user_id', Auth::id())
            ->where('read', false)
            ->count();

        return response()->json(['count' => $count]);
    }

    /**
     * Récupérer toutes les notifications pour l'utilisateur connecté
     */
    public function getAll()
    {
        $notifications = Notification::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        return view('notifications.index', compact('notifications'))->with('userTheme', $theme);
    }

    /**
     * Marquer une notification comme lue
     */
    public function markAsRead($id)
    {
        $notification = Notification::findOrFail($id);

        // Vérifier que la notification appartient à l'utilisateur connecté
        if ($notification->user_id != Auth::id()) {
            return response()->json(['error' => 'Non autorisé'], 403);
        }

        $notification->read = true;
        $notification->save();

        return response()->json(['success' => true]);
    }

    /**
     * Marquer toutes les notifications comme lues
     */
    public function markAllAsRead()
    {
        Notification::where('user_id', Auth::id())
            ->where('read', false)
            ->update(['read' => true]);

        return response()->json(['success' => true]);
    }
}
