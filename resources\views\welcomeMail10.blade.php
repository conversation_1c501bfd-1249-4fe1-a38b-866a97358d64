<!DOCTYPE html>
<html>
<head>
    <title>Bienvenue sur TaskFlow</title>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Base styles */
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            padding: 20px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .logo {
            max-width: 150px;
            height: auto;
        }
        .content {
            padding: 30px 20px;
        }
        .footer {
            text-align: center;
            padding: 20px;
            font-size: 12px;
            color: #6c757d;
            border-top: 1px solid #e9ecef;
        }
        h1 {
            color: #0d6efd;
            margin-bottom: 20px;
        }
        p {
            margin-bottom: 15px;
        }
        .btn {
            display: inline-block;
            font-weight: 500;
            text-align: center;
            white-space: nowrap;
            vertical-align: middle;
            user-select: none;
            border: 1px solid transparent;
            padding: 0.75rem 1.5rem;
            font-size: 16px;
            line-height: 1.5;
            border-radius: 0.3rem;
            text-decoration: none;
            transition: all 0.15s ease-in-out;
            margin: 20px 0;
        }
        .btn-primary {
            color: #fff;
            background-color: #0d6efd;
            border-color: #0d6efd;
        }
        .btn-primary:hover {
            background-color: #0b5ed7;
            border-color: #0a58ca;
        }
        .card {
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin-top: 20px;
            background-color: #f8f9fa;
        }
        .highlight {
            font-weight: bold;
            color: #0d6efd;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="https://taskflow.com/assets/images/newnewnew.png" alt="TaskFlow Logo" class="logo">
            <h1>Bienvenue sur TaskFlow</h1>
        </div>

        <div class="content">
            <h2>Bonjour {{ $name }},</h2>

            <p>Nous sommes ravis de vous accueillir sur <span class="highlight">TaskFlow</span>, votre nouvelle plateforme de gestion de projets et de tâches !</p>

            <p>Votre compte a été créé avec succès. Voici vos informations de connexion :</p>

            <div class="card" style="margin-bottom: 20px;">
                <p><strong>Email :</strong> {{ $email }}</p>
                <p><strong>Mot de passe :</strong> {{ $password }}</p>
            </div>

            <p>Vous pouvez maintenant vous connecter à votre espace client et commencer à suivre vos projets.</p>

            <div style="text-align: center;">
                <a href="http://127.0.0.1:8000/login" class="btn btn-primary">Se connecter</a>
            </div>

            <div class="card">
                <h3>Premiers pas avec TaskFlow</h3>
                <ul>
                    <li>Créez votre premier projet</li>
                    <li>Ajoutez des tâches et assignez-les à votre équipe</li>
                    <li>Suivez la progression de vos projets</li>
                    <li>Utilisez le tableau Kanban pour visualiser votre flux de travail</li>
                </ul>
            </div>

            <p>Si vous avez des questions, n'hésitez pas à contacter notre équipe de support.</p>
        </div>

        <div class="footer">
            <p>&copy; {{ date('Y') }} TaskFlow. Tous droits réservés.</p>
            <p>Cet email a été envoyé automatiquement, merci de ne pas y répondre.</p>
        </div>
    </div>
</body>
</html>
