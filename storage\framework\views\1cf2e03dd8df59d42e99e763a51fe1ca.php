

<?php $__env->startSection('contenu'); ?>

<div class="container-fluid">

    <?php if(session('message')): ?>
        <div class="alert alert-success alert-dismissible fade show mt-3" role="alert">
            <?php echo e(session('message')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
        </div>
    <?php endif; ?>

    <?php if(session('error')): ?>
        <div class="alert alert-danger alert-dismissible fade show mt-3" role="alert">
            <?php echo e(session('error')); ?>

            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Fermer"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1"><?php echo e(__('message.Project list')); ?></h4>
                    <div class="flex-shrink-0">
                        <a href="/projet" class="btn btn-success">
                            <?php echo e(__('message.Add project')); ?> <i class="bx bx-plus"></i>
                        </a>
                    </div>
                </div><!-- fin entête de carte -->

                <div class="card-body">
                    <div class="live-preview">
                        <div class="table-responsive">

                            <table class="table align-middle table-nowrap mb-0">
                                <thead>
                                    <tr>
                                        <th scope="col"><?php echo e(__('message.id')); ?></th>
                                        <th scope="col"><?php echo e(__('message.project_name')); ?></th>
                                        <th scope="col"><?php echo e(__('message.start_date')); ?></th>
                                        <th scope="col"><?php echo e(__('message.end_date')); ?></th>
                                        <th scope="col">Kanban</th>
                                        <th scope="col"><?php echo e(__('message.action')); ?></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $projects; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $item): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <tr>
                                        <th scope="row"><a href="#" class="fw-medium">#<?php echo e($item->id); ?></a></th>
                                        <td><?php echo e($item->project_name); ?></td>
                                        <td><?php echo e($item->start_date); ?></td>
                                        <td><?php echo e($item->end_date ?? 'N/A'); ?></td>
                                        <td>
                                            <a href="<?php echo e(route('entreprise.dashboard', ['projet_id' => $item->id])); ?>" class="btn btn-info btn-sm" title="Voir le Kanban de ce projet">
                                                <i class="ri-kanban-view me-1"></i> Kanban
                                            </a>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?php echo e(route('projet-detail', ['id' => $item->id])); ?>" class="btn btn-success btn-sm" title="Voir les détails">
                                                    <i class="bx bx-show"></i> Détails
                                                </a>
                                                <a href="/modifier-projet/<?php echo e($item->id); ?>" type="button" class="btn btn-primary btn-sm">
                                                    <i class="bx bx-edit-alt"></i> <?php echo e(__('message.edit')); ?>

                                                </a>
                                                <a href="/supprimer-projet/<?php echo e($item->id); ?>" class="btn btn-danger btn-sm" onclick="return confirm('<?php echo e(__('message.Are you sure you want to delete this project?')); ?>')">
                                                    <i class="bx bx-trash"></i> <?php echo e(__('message.delete')); ?>

                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>

                </div><!-- fin du corps de la carte -->
            </div><!-- fin de la carte -->
        </div>
        <!-- fin col -->
    </div>
    <!-- fin ligne -->

</div>

<?php $__env->stopSection(); ?>

<style>
/* Styles pour améliorer l'apparence des boutons */
.btn-group .btn {
    margin-right: 2px;
}

.btn-group .btn:last-child {
    margin-right: 0;
}

/* Animation pour les boutons Kanban */
.btn-info {
    transition: all 0.3s ease;
}

.btn-info:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
}

/* Amélioration de l'apparence du tableau */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
}

.table td {
    vertical-align: middle;
}

/* Style pour les liens d'ID */
.fw-medium {
    color: #0d6efd;
    text-decoration: none;
}

.fw-medium:hover {
    color: #0a58ca;
    text-decoration: underline;
}
</style>

<?php echo $__env->make($userTheme ?? 'theme', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\task\resources\views/liste-projet.blade.php ENDPATH**/ ?>