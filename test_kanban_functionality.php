<?php
/**
 * Script de test pour vérifier les fonctionnalités Kanban
 * À exécuter avec : php artisan tinker < test_kanban_functionality.php
 */

echo "=== Test des Fonctionnalités Kanban ===\n";

// Test 1: Vérifier les routes
echo "\n1. Vérification des routes...\n";
try {
    $routes = collect(Route::getRoutes())->map(function ($route) {
        return [
            'name' => $route->getName(),
            'uri' => $route->uri(),
            'methods' => implode('|', $route->methods())
        ];
    });
    
    $projectsListRoute = $routes->where('name', 'projects.list')->first();
    $entrepriseDashRoute = $routes->where('name', 'entreprise.dashboard')->first();
    $projetDetailRoute = $routes->where('name', 'projet-detail')->first();
    
    echo "✓ Route projects.list: " . ($projectsListRoute ? "OK" : "MANQUANTE") . "\n";
    echo "✓ Route entreprise.dashboard: " . ($entrepriseDashRoute ? "OK" : "MANQUANTE") . "\n";
    echo "✓ Route projet-detail: " . ($projetDetailRoute ? "OK" : "MANQUANTE") . "\n";
} catch (Exception $e) {
    echo "✗ Erreur lors de la vérification des routes: " . $e->getMessage() . "\n";
}

// Test 2: Vérifier les modèles et relations
echo "\n2. Vérification des modèles et relations...\n";
try {
    // Vérifier le modèle Projet
    $projetCount = App\Models\Projet::count();
    echo "✓ Nombre de projets en base: $projetCount\n";
    
    // Vérifier les relations
    $projetAvecTaches = App\Models\Projet::with('taches')->first();
    if ($projetAvecTaches) {
        echo "✓ Relation Projet->taches: OK\n";
        echo "  - Projet: " . $projetAvecTaches->project_name . "\n";
        echo "  - Nombre de tâches: " . $projetAvecTaches->taches->count() . "\n";
    } else {
        echo "! Aucun projet trouvé pour tester les relations\n";
    }
    
    // Vérifier le modèle Tache
    $tacheCount = App\Models\Tache::count();
    echo "✓ Nombre de tâches en base: $tacheCount\n";
    
} catch (Exception $e) {
    echo "✗ Erreur lors de la vérification des modèles: " . $e->getMessage() . "\n";
}

// Test 3: Vérifier les contrôleurs
echo "\n3. Vérification des contrôleurs...\n";
try {
    // Vérifier ProjetController
    if (class_exists('App\Http\Controllers\ProjetController')) {
        echo "✓ ProjetController: OK\n";
        $controller = new App\Http\Controllers\ProjetController();
        if (method_exists($controller, 'getProjet')) {
            echo "  - Méthode getProjet: OK\n";
        }
    }
    
    // Vérifier EntreprisedashController
    if (class_exists('App\Http\Controllers\EntreprisedashController')) {
        echo "✓ EntreprisedashController: OK\n";
        $controller = new App\Http\Controllers\EntreprisedashController();
        if (method_exists($controller, 'index')) {
            echo "  - Méthode index: OK\n";
        }
    }
    
    // Vérifier TacheDetController
    if (class_exists('App\Http\Controllers\TacheDetController')) {
        echo "✓ TacheDetController: OK\n";
        $controller = new App\Http\Controllers\TacheDetController();
        if (method_exists($controller, 'showProjet')) {
            echo "  - Méthode showProjet: OK\n";
        }
    }
    
} catch (Exception $e) {
    echo "✗ Erreur lors de la vérification des contrôleurs: " . $e->getMessage() . "\n";
}

// Test 4: Vérifier les vues
echo "\n4. Vérification des vues...\n";
$vues = [
    'liste-projet.blade.php',
    'projet-detail.blade.php',
    'dash-entreprise.blade.php'
];

foreach ($vues as $vue) {
    $chemin = resource_path("views/$vue");
    if (file_exists($chemin)) {
        echo "✓ Vue $vue: OK\n";
        
        // Vérifier le contenu spécifique
        $contenu = file_get_contents($chemin);
        
        if ($vue === 'liste-projet.blade.php') {
            if (strpos($contenu, 'Kanban') !== false) {
                echo "  - Colonne Kanban: OK\n";
            }
            if (strpos($contenu, 'entreprise.dashboard') !== false) {
                echo "  - Lien vers dashboard: OK\n";
            }
        }
        
        if ($vue === 'projet-detail.blade.php') {
            if (strpos($contenu, 'Voir Kanban') !== false) {
                echo "  - Bouton Voir Kanban: OK\n";
            }
        }
        
        if ($vue === 'dash-entreprise.blade.php') {
            if (strpos($contenu, 'Première tâche') !== false) {
                echo "  - Bouton Première tâche: OK\n";
            }
        }
    } else {
        echo "✗ Vue $vue: MANQUANTE\n";
    }
}

// Test 5: Simulation d'un scénario utilisateur
echo "\n5. Simulation d'un scénario utilisateur...\n";
try {
    // Créer un utilisateur entreprise de test (si pas déjà existant)
    $user = App\Models\User::where('role', 'entreprise')->first();
    if (!$user) {
        echo "! Aucun utilisateur entreprise trouvé pour le test\n";
    } else {
        echo "✓ Utilisateur entreprise trouvé: " . $user->name . "\n";
        
        // Vérifier ses projets
        $projets = App\Models\Projet::where('user_id', $user->id)->get();
        echo "✓ Projets de l'utilisateur: " . $projets->count() . "\n";
        
        foreach ($projets as $projet) {
            echo "  - " . $projet->project_name . " (" . $projet->taches->count() . " tâches)\n";
        }
    }
} catch (Exception $e) {
    echo "✗ Erreur lors de la simulation: " . $e->getMessage() . "\n";
}

echo "\n=== Fin des tests ===\n";
echo "Si tous les tests sont OK, les fonctionnalités Kanban sont opérationnelles!\n";
