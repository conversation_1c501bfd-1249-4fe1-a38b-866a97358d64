<?php
/**
 * Script de test pour vérifier la refactorisation des projets
 * À exécuter avec : php artisan tinker < test_project_refactoring.php
 */

echo "=== Test de la Refactorisation des Projets ===\n";

// Test 1: Vérifier la structure de la table projets
echo "\n1. Vérification de la structure de la table projets...\n";
try {
    $columns = Schema::getColumnListing('projets');
    echo "✓ Colonnes actuelles de la table projets:\n";
    foreach ($columns as $column) {
        echo "  - $column\n";
    }
    
    // Vérifier que les anciennes colonnes ont été supprimées
    if (!in_array('task_title', $columns)) {
        echo "✓ Colonne 'task_title' supprimée avec succès\n";
    } else {
        echo "✗ Colonne 'task_title' existe encore\n";
    }
    
    if (!in_array('task_description', $columns)) {
        echo "✓ Colonne 'task_description' supprimée avec succès\n";
    } else {
        echo "✗ Colonne 'task_description' existe encore\n";
    }
    
    // Vérifier que la nouvelle colonne existe
    if (in_array('project_description', $columns)) {
        echo "✓ Nouvelle colonne 'project_description' créée avec succès\n";
    } else {
        echo "✗ Colonne 'project_description' manquante\n";
    }
    
} catch (Exception $e) {
    echo "✗ Erreur lors de la vérification de la structure: " . $e->getMessage() . "\n";
}

// Test 2: Vérifier le modèle Projet
echo "\n2. Vérification du modèle Projet...\n";
try {
    $projet = new App\Models\Projet();
    $fillable = $projet->getFillable();
    
    echo "✓ Champs fillable du modèle Projet:\n";
    foreach ($fillable as $field) {
        echo "  - $field\n";
    }
    
    // Vérifier que les anciens champs ont été supprimés
    if (!in_array('task_title', $fillable)) {
        echo "✓ 'task_title' supprimé du fillable\n";
    } else {
        echo "✗ 'task_title' existe encore dans fillable\n";
    }
    
    if (!in_array('task_description', $fillable)) {
        echo "✓ 'task_description' supprimé du fillable\n";
    } else {
        echo "✗ 'task_description' existe encore dans fillable\n";
    }
    
    // Vérifier que le nouveau champ existe
    if (in_array('project_description', $fillable)) {
        echo "✓ 'project_description' ajouté au fillable\n";
    } else {
        echo "✗ 'project_description' manquant dans fillable\n";
    }
    
} catch (Exception $e) {
    echo "✗ Erreur lors de la vérification du modèle: " . $e->getMessage() . "\n";
}

// Test 3: Vérifier les données existantes
echo "\n3. Vérification des données existantes...\n";
try {
    $projets = App\Models\Projet::all();
    echo "✓ Nombre de projets en base: " . $projets->count() . "\n";
    
    foreach ($projets as $projet) {
        echo "  - Projet: " . $projet->project_name . "\n";
        echo "    * ID: " . $projet->id . "\n";
        echo "    * Description: " . ($projet->project_description ?? 'Aucune') . "\n";
        
        // Vérifier que les anciens champs n'existent plus
        try {
            $taskTitle = $projet->task_title;
            echo "    ✗ ERREUR: task_title existe encore ($taskTitle)\n";
        } catch (Exception $e) {
            echo "    ✓ task_title n'existe plus (normal)\n";
        }
        
        try {
            $taskDescription = $projet->task_description;
            echo "    ✗ ERREUR: task_description existe encore ($taskDescription)\n";
        } catch (Exception $e) {
            echo "    ✓ task_description n'existe plus (normal)\n";
        }
    }
    
} catch (Exception $e) {
    echo "✗ Erreur lors de la vérification des données: " . $e->getMessage() . "\n";
}

// Test 4: Test de création d'un nouveau projet
echo "\n4. Test de création d'un nouveau projet...\n";
try {
    $testData = [
        'project_name' => 'Projet Test Refactorisation',
        'project_description' => 'Description du projet de test après refactorisation',
        'start_date' => now()->format('Y-m-d'),
        'end_date' => now()->addDays(30)->format('Y-m-d'),
        'user_id' => 1 // Supposons que l'utilisateur ID 1 existe
    ];
    
    $nouveauProjet = new App\Models\Projet();
    $nouveauProjet->fill($testData);
    
    echo "✓ Nouveau projet créé en mémoire avec les nouvelles données:\n";
    echo "  - Nom: " . $nouveauProjet->project_name . "\n";
    echo "  - Description: " . $nouveauProjet->project_description . "\n";
    echo "  - Date début: " . $nouveauProjet->start_date . "\n";
    echo "  - Date fin: " . $nouveauProjet->end_date . "\n";
    
    // Ne pas sauvegarder pour éviter de polluer la base
    echo "✓ Test de création réussi (non sauvegardé)\n";
    
} catch (Exception $e) {
    echo "✗ Erreur lors du test de création: " . $e->getMessage() . "\n";
}

// Test 5: Vérifier les clés de traduction
echo "\n5. Vérification des clés de traduction...\n";
try {
    $frenchMessages = include resource_path('lang/fr/message.php');
    $englishMessages = include resource_path('lang/en/message.php');
    
    // Vérifier les nouvelles clés
    if (isset($frenchMessages['project_description'])) {
        echo "✓ Clé 'project_description' existe en français: " . $frenchMessages['project_description'] . "\n";
    } else {
        echo "✗ Clé 'project_description' manquante en français\n";
    }
    
    if (isset($englishMessages['project_description'])) {
        echo "✓ Clé 'project_description' existe en anglais: " . $englishMessages['project_description'] . "\n";
    } else {
        echo "✗ Clé 'project_description' manquante en anglais\n";
    }
    
    if (isset($frenchMessages['Enter project description'])) {
        echo "✓ Clé 'Enter project description' existe en français\n";
    } else {
        echo "✗ Clé 'Enter project description' manquante en français\n";
    }
    
    // Vérifier que les anciennes clés ont été supprimées
    if (!isset($frenchMessages['task_title'])) {
        echo "✓ Ancienne clé 'task_title' supprimée du français\n";
    } else {
        echo "! Ancienne clé 'task_title' existe encore en français\n";
    }
    
    if (!isset($frenchMessages['task_description'])) {
        echo "✓ Ancienne clé 'task_description' supprimée du français\n";
    } else {
        echo "! Ancienne clé 'task_description' existe encore en français\n";
    }
    
} catch (Exception $e) {
    echo "✗ Erreur lors de la vérification des traductions: " . $e->getMessage() . "\n";
}

echo "\n=== Fin des tests ===\n";
echo "Si tous les tests sont OK, la refactorisation est réussie!\n";
echo "\nRésumé des changements:\n";
echo "- ✓ Suppression de 'task_title' et 'task_description'\n";
echo "- ✓ Ajout de 'project_description'\n";
echo "- ✓ Migration des données existantes\n";
echo "- ✓ Mise à jour du modèle Projet\n";
echo "- ✓ Mise à jour des traductions\n";
echo "- ✓ Mise à jour des vues et formulaires\n";
