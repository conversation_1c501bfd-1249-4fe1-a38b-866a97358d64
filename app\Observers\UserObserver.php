<?php

namespace App\Observers;

use App\Models\User;
use App\Helpers\AvatarHelper;

class UserObserver
{
    /**
     * Handle the User "created" event.
     */
    public function created(User $user): void
    {
        try {
            // Générer automatiquement l'avatar lors de la création d'un utilisateur
            if (empty($user->avatar) || empty($user->avatar_color)) {
                AvatarHelper::updateUserAvatar($user);
            }
        } catch (\Exception $e) {
            // Log l'erreur mais ne pas faire échouer la création de l'utilisateur
            \Log::warning('Erreur lors de la génération d\'avatar pour l\'utilisateur ' . $user->id . ': ' . $e->getMessage());
        }
    }

    /**
     * Handle the User "updated" event.
     */
    public function updated(User $user): void
    {
        try {
            // Régénérer l'avatar si le nom a changé
            if ($user->isDirty('name') && !empty($user->name)) {
                AvatarHelper::updateUserAvatar($user);
            }
        } catch (\Exception $e) {
            // Log l'erreur mais ne pas faire échouer la mise à jour de l'utilisateur
            \Log::warning('Erreur lors de la mise à jour d\'avatar pour l\'utilisateur ' . $user->id . ': ' . $e->getMessage());
        }
    }

    /**
     * Handle the User "deleted" event.
     */
    public function deleted(User $user): void
    {
        //
    }

    /**
     * Handle the User "restored" event.
     */
    public function restored(User $user): void
    {
        //
    }

    /**
     * Handle the User "force deleted" event.
     */
    public function forceDeleted(User $user): void
    {
        //
    }
}
