<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Tache;
use Illuminate\Support\Facades\Auth;

class PersonnelDashController extends Controller
{
    /**
     * Affiche le tableau de bord du personnel
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        // Récupérer les tâches assignées au membre connecté
        $taches = Tache::with('projet')
                    ->where('personnel_id', Auth::id())
                    ->orderBy('deadline', 'asc')
                    ->get();

        // Déterminer le thème en fonction du rôle de l'utilisateur
        $theme = \App\Helpers\ThemeHelper::getUserTheme();

        // Retourner la vue avec le thème approprié
        return view('dash-personnel', compact('taches'))->with('userTheme', $theme);
    }
}
