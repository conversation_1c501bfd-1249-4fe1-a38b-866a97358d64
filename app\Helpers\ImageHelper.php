<?php

namespace App\Helpers;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;

class ImageHelper
{
    /**
     * Dossiers de stockage pour différents types d'images
     */
    const FOLDERS = [
        'projects' => 'images/projects',
        'tasks' => 'images/tasks',
        'avatars' => 'images/avatars',
        'profiles' => 'images/profiles',
    ];

    /**
     * Tailles d'images autorisées
     */
    const SIZES = [
        'thumbnail' => [150, 150],
        'small' => [300, 300],
        'medium' => [600, 600],
        'large' => [1200, 1200],
    ];

    /**
     * Upload et traitement d'une image
     *
     * @param UploadedFile $file
     * @param string $folder
     * @param array $sizes
     * @return array
     */
    public static function uploadImage(UploadedFile $file, $folder = 'projects', $sizes = ['original', 'thumbnail'])
    {
        // Validation du fichier
        if (!self::validateImage($file)) {
            throw new \Exception('Fichier image invalide');
        }

        $folderPath = self::FOLDERS[$folder] ?? 'images/misc';
        $filename = self::generateUniqueFilename($file);
        
        $uploadedFiles = [];

        foreach ($sizes as $size) {
            if ($size === 'original') {
                // Stocker l'image originale
                $path = $file->storeAs($folderPath, $filename, 'public');
                $uploadedFiles['original'] = $path;
            } else {
                // Redimensionner et stocker
                $resizedPath = self::resizeAndStore($file, $folderPath, $filename, $size);
                $uploadedFiles[$size] = $resizedPath;
            }
        }

        return $uploadedFiles;
    }

    /**
     * Valider un fichier image
     *
     * @param UploadedFile $file
     * @return bool
     */
    public static function validateImage(UploadedFile $file)
    {
        // Vérifier le type MIME
        $allowedMimes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            return false;
        }

        // Vérifier la taille (max 5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            return false;
        }

        // Vérifier les dimensions
        $imageInfo = getimagesize($file->getPathname());
        if (!$imageInfo || $imageInfo[0] > 4000 || $imageInfo[1] > 4000) {
            return false;
        }

        return true;
    }

    /**
     * Générer un nom de fichier unique
     *
     * @param UploadedFile $file
     * @return string
     */
    public static function generateUniqueFilename(UploadedFile $file)
    {
        $extension = $file->getClientOriginalExtension();
        $name = pathinfo($file->getClientOriginalName(), PATHINFO_FILENAME);
        $name = preg_replace('/[^a-zA-Z0-9_-]/', '', $name);
        
        return $name . '_' . time() . '_' . uniqid() . '.' . $extension;
    }

    /**
     * Redimensionner et stocker une image
     *
     * @param UploadedFile $file
     * @param string $folderPath
     * @param string $filename
     * @param string $size
     * @return string
     */
    public static function resizeAndStore(UploadedFile $file, $folderPath, $filename, $size)
    {
        if (!isset(self::SIZES[$size])) {
            throw new \Exception("Taille d'image non supportée: $size");
        }

        [$width, $height] = self::SIZES[$size];
        
        // Créer le nom de fichier avec le suffixe de taille
        $sizedFilename = pathinfo($filename, PATHINFO_FILENAME) . "_{$size}." . pathinfo($filename, PATHINFO_EXTENSION);
        
        // Chemin complet
        $fullPath = storage_path('app/public/' . $folderPath . '/' . $sizedFilename);
        
        // Créer le dossier s'il n'existe pas
        $directory = dirname($fullPath);
        if (!file_exists($directory)) {
            mkdir($directory, 0755, true);
        }

        // Redimensionner l'image (nécessite Intervention Image)
        try {
            $manager = new ImageManager(new Driver());
            $image = $manager->read($file->getPathname());
            $image = $image->cover($width, $height);
            $image->save($fullPath, 85); // Qualité 85%

            return $folderPath . '/' . $sizedFilename;
        } catch (\Exception $e) {
            // Fallback: copier l'image originale
            copy($file->getPathname(), $fullPath);
            return $folderPath . '/' . $sizedFilename;
        }
    }

    /**
     * Obtenir l'URL d'une image
     *
     * @param string|null $path
     * @param string $size
     * @param string $default
     * @return string
     */
    public static function getImageUrl($path, $size = 'original', $default = null)
    {
        if (!$path) {
            return $default ?: asset('assets/images/default-project.jpg');
        }

        if ($size === 'original') {
            return Storage::url($path);
        }

        // Construire le chemin pour la taille spécifique
        $pathInfo = pathinfo($path);
        $sizedPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . "_{$size}." . $pathInfo['extension'];
        
        if (Storage::disk('public')->exists($sizedPath)) {
            return Storage::url($sizedPath);
        }

        // Fallback vers l'image originale
        return Storage::url($path);
    }

    /**
     * Supprimer une image et toutes ses variantes
     *
     * @param string $path
     * @return bool
     */
    public static function deleteImage($path)
    {
        if (!$path) {
            return true;
        }

        $deleted = true;
        
        // Supprimer l'image originale
        if (Storage::disk('public')->exists($path)) {
            $deleted = Storage::disk('public')->delete($path) && $deleted;
        }

        // Supprimer les variantes
        $pathInfo = pathinfo($path);
        foreach (array_keys(self::SIZES) as $size) {
            $sizedPath = $pathInfo['dirname'] . '/' . $pathInfo['filename'] . "_{$size}." . $pathInfo['extension'];
            if (Storage::disk('public')->exists($sizedPath)) {
                $deleted = Storage::disk('public')->delete($sizedPath) && $deleted;
            }
        }

        return $deleted;
    }

    /**
     * Obtenir les informations d'une image
     *
     * @param string $path
     * @return array|null
     */
    public static function getImageInfo($path)
    {
        if (!$path || !Storage::disk('public')->exists($path)) {
            return null;
        }

        $fullPath = Storage::disk('public')->path($path);
        $imageInfo = getimagesize($fullPath);
        
        if (!$imageInfo) {
            return null;
        }

        return [
            'width' => $imageInfo[0],
            'height' => $imageInfo[1],
            'mime' => $imageInfo['mime'],
            'size' => Storage::disk('public')->size($path),
            'url' => Storage::url($path),
        ];
    }

    /**
     * Créer une image placeholder
     *
     * @param int $width
     * @param int $height
     * @param string $text
     * @param string $bgColor
     * @param string $textColor
     * @return string
     */
    public static function createPlaceholder($width = 300, $height = 200, $text = 'No Image', $bgColor = '#f8f9fa', $textColor = '#6c757d')
    {
        return "https://via.placeholder.com/{$width}x{$height}/{$bgColor}/{$textColor}?text=" . urlencode($text);
    }
}
