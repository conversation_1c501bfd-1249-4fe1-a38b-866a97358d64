@extends($userTheme ?? 'theme')
@section('contenu')

<!-- début du titre de la page -->
<!-- fin du titre de la page -->

<div class="row">
    <div class="col-lg-12">
        <div class="card">
            <div class="card-header align-items-center d-flex">
                <h4 class="card-title mb-0 flex-grow-1">{{ __('message.Add Administrator') }}</h4>
                <div class="flex-shrink-0">
                </div>
            </div>
            <div class="card-body">
                <!-- Affichage des messages d'erreur -->
                @if(session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <i class="ri-error-warning-line me-1 align-middle"></i> {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if(session('message'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <i class="ri-check-double-line me-1 align-middle"></i> {{ session('message') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if ($errors->any())
                    <div class="alert alert-danger">
                        <ul class="mb-0">
                            @foreach ($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <div class="live-preview">
                    <div class="row gy-4">
                        <form action="/ajoutadmin" method="POST">
                            @csrf

                            <div class="col-xxl-12 col-md-12">
                                <div>
                                    <label for="labelInput" class="form-label">{{ __('message.Name_admin') }} <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control @error('name') is-invalid @enderror" id="labelInput" name="name" value="{{ old('name') }}" required>
                                    @error('name')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                            </div>

                            <div class="col-xxl-12 col-md-12">
                                <div>
                                    <label for="iconInput" class="form-label">{{ __('message.Email') }} <span class="text-danger">*</span></label>
                                    <div class="form-icon">
                                        <input type="email" class="form-control form-control-icon @error('email') is-invalid @enderror" id="iconInput" placeholder="<EMAIL>" name="email" value="{{ old('email') }}" required>
                                        <i class="ri-mail-unread-line"></i>
                                        @error('email')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <div class="col-xxl-12 col-md-12">
                                <div>
                                    <label for="exampleInputpassword" class="form-label">{{ __('message.Password') }} <span class="text-danger">*</span></label>
                                    <input type="password" class="form-control @error('password') is-invalid @enderror" id="exampleInputpassword" name="password" required minlength="6">
                                    @error('password')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">Le mot de passe doit contenir au moins 6 caractères.</div>
                                </div>
                            </div>

                            <div>
                                <div class="col-xxl-12 col-md-12">
                                    <button type="submit" class="btn btn-primary">{{ __('message.Add_admin') }}</button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="d-none code-view">
                    <pre class="language-markup" style="height: 450px;">
<code>&lt;!-- Champ de saisie de base --&gt;
                    </code></pre>
                </div>
            </div>
        </div>
    </div>
    <!-- fin de col -->
</div>
<!-- fin de row -->

@endsection
