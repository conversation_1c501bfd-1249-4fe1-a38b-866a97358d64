<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Vérifier si une langue est définie en session
        if (session()->has('locale')) {
            $locale = session('locale');

            // Vérifier si la langue est supportée
            if (in_array($locale, ['en', 'fr'])) {
                App::setLocale($locale);
            } else {
                // Si la langue n'est pas supportée, utiliser le français par défaut
                App::setLocale('fr');
                session(['locale' => 'fr']);
            }
        } else {
            // Définir explicitement le français comme langue par défaut
            App::setLocale('fr');
            session(['locale' => 'fr']);
        }

        return $next($request);
    }
}
