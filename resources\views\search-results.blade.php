@extends('theme')
@section('contenu')
<div class="container-fluid">
    <div class="row">
        <div class="col-lg-12">
            <div class="card">
                <div class="card-header">
                    <h4 class="card-title mb-0">{{ __('message.Search Results') }}</h4>
                </div>
                <div class="card-body">
                    <div class="row justify-content-center mb-4">
                        <div class="col-lg-6">
                            <form action="{{ route('search') }}" method="GET" class="search-form">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="query" value="{{ $query }}" placeholder="{{ __('message.Search') }}...">
                                    <button class="btn btn-primary" type="submit">
                                        <i class="ri-search-line"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <div class="d-flex align-items-center mb-4">
                        <h5 class="mb-0 flex-grow-1">{{ __('message.Number of results') }}: <span class="text-primary">{{ $count }}</span></h5>
                    </div>

                    @if($count > 0)
                        <!-- Utilisateurs -->
                        @if(isset($results['users']) && $results['users']->count() > 0)
                            <div class="search-result-section mb-4">
                                <h5 class="border-bottom pb-2">{{ __('message.Users') }} ({{ $results['users']->count() }})</h5>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>{{ __('message.Name') }}</th>
                                                <th>{{ __('message.Email') }}</th>
                                                <th>{{ __('message.Role') }}</th>
                                                <th>{{ __('message.Actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($results['users'] as $user)
                                                <tr>
                                                    <td>{{ $user->name }}</td>
                                                    <td>{{ $user->email }}</td>
                                                    <td>
                                                        @if($user->role == 'admin')
                                                            <span class="badge bg-primary">{{ __('message.Administrator') }}</span>
                                                        @elseif($user->role == 'entreprise')
                                                            <span class="badge bg-success">{{ __('message.Company') }}</span>
                                                        @else
                                                            <span class="badge bg-info">{{ __('message.Personnel') }}</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        @if($user->role == 'admin')
                                                            <a href="/modifier-admin/{{ $user->id }}" class="btn btn-sm btn-primary">
                                                                <i class="ri-eye-line"></i> {{ __('message.View') }}
                                                            </a>
                                                        @endif
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        @endif

                        <!-- Projets -->
                        @if(isset($results['projets']) && $results['projets']->count() > 0)
                            <div class="search-result-section mb-4">
                                <h5 class="border-bottom pb-2">{{ __('message.Projects') }} ({{ $results['projets']->count() }})</h5>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>{{ __('message.Project Name') }}</th>
                                                <th>{{ __('message.Task Title') }}</th>
                                                <th>{{ __('message.Start Date') }}</th>
                                                <th>{{ __('message.Actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($results['projets'] as $projet)
                                                <tr>
                                                    <td>{{ $projet->project_name }}</td>
                                                    <td>{{ $projet->task_title }}</td>
                                                    <td>{{ $projet->start_date }}</td>
                                                    <td>
                                                        <a href="/projet/edit/{{ $projet->id }}" class="btn btn-sm btn-primary">
                                                            <i class="ri-eye-line"></i> {{ __('message.View') }}
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        @endif

                        <!-- Tâches -->
                        @if(isset($results['taches']) && $results['taches']->count() > 0)
                            <div class="search-result-section mb-4">
                                <h5 class="border-bottom pb-2">{{ __('message.Tasks') }} ({{ $results['taches']->count() }})</h5>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>{{ __('message.Name') }}</th>
                                                <th>{{ __('message.Description') }}</th>
                                                <th>{{ __('message.Status') }}</th>
                                                <th>{{ __('message.Actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($results['taches'] as $tache)
                                                <tr>
                                                    <td>{{ $tache->nom }}</td>
                                                    <td>{{ Str::limit($tache->description, 50) }}</td>
                                                    <td>
                                                        @if($tache->statut == 'to_do')
                                                            <span class="badge bg-warning">{{ __('message.To do') }}</span>
                                                        @elseif($tache->statut == 'doing')
                                                            <span class="badge bg-info">{{ __('message.In progress') }}</span>
                                                        @elseif($tache->statut == 'bug')
                                                            <span class="badge bg-danger">{{ __('message.Bug') }}</span>
                                                        @elseif($tache->statut == 'done')
                                                            <span class="badge bg-success">{{ __('message.Finished') }}</span>
                                                        @endif
                                                    </td>
                                                    <td>
                                                        <a href="/tache-detail/{{ $tache->id }}" class="btn btn-sm btn-primary">
                                                            <i class="ri-eye-line"></i> {{ __('message.View') }}
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        @endif

                        <!-- Contacts -->
                        @if(isset($results['contacts']) && $results['contacts']->count() > 0)
                            <div class="search-result-section mb-4">
                                <h5 class="border-bottom pb-2">{{ __('message.Contact') }} ({{ $results['contacts']->count() }})</h5>
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>{{ __('message.Name') }}</th>
                                                <th>{{ __('message.Email') }}</th>
                                                <th>{{ __('message.Subject') }}</th>
                                                <th>{{ __('message.Actions') }}</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach($results['contacts'] as $contact)
                                                <tr>
                                                    <td>{{ $contact->name }}</td>
                                                    <td>{{ $contact->email }}</td>
                                                    <td>{{ $contact->sujet }}</td>
                                                    <td>
                                                        <a href="/afficher-message/{{ $contact->id }}" class="btn btn-sm btn-primary">
                                                            <i class="ri-eye-line"></i> {{ __('message.View') }}
                                                        </a>
                                                    </td>
                                                </tr>
                                            @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        @endif
                    @else
                        <div class="text-center py-5">
                            <div class="avatar-lg mx-auto mb-4">
                                <div class="avatar-title bg-light text-primary display-5 rounded-circle">
                                    <i class="ri-search-line"></i>
                                </div>
                            </div>
                            <h5>{{ __('message.No results found') }}</h5>
                            <p class="text-muted">{{ __('message.Try different keywords or filters') }}</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
