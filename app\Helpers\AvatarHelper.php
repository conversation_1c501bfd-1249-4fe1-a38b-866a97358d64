<?php

namespace App\Helpers;

class AvatarHelper
{
    /**
     * Couleurs disponibles pour les avatars
     */
    private static $colors = [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
        '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
        '#A3E4D7', '#F9E79F', '#FADBD8', '#D5DBDB', '#AED6F1'
    ];

    /**
     * Génère la première lettre du nom pour l'avatar
     */
    public static function getInitials($name)
    {
        if (empty($name)) {
            return 'U';
        }

        $words = explode(' ', trim($name));
        
        if (count($words) >= 2) {
            // Si il y a au moins 2 mots, prendre la première lettre de chaque
            return strtoupper(substr($words[0], 0, 1) . substr($words[1], 0, 1));
        } else {
            // Sinon, prendre les 2 premières lettres du premier mot
            return strtoupper(substr($words[0], 0, 2));
        }
    }

    /**
     * Génère une couleur basée sur le nom
     */
    public static function getColorFromName($name)
    {
        $hash = md5($name);
        $index = hexdec(substr($hash, 0, 2)) % count(self::$colors);
        return self::$colors[$index];
    }

    /**
     * Génère l'avatar complet (initiales + couleur)
     */
    public static function generateAvatar($name)
    {
        return [
            'initials' => self::getInitials($name),
            'color' => self::getColorFromName($name)
        ];
    }

    /**
     * Génère le HTML pour l'avatar
     */
    public static function getAvatarHtml($name, $size = 'md', $additionalClasses = '')
    {
        $avatar = self::generateAvatar($name);
        
        $sizeClasses = [
            'xs' => 'avatar-xs',
            'sm' => 'avatar-sm', 
            'md' => 'avatar-md',
            'lg' => 'avatar-lg',
            'xl' => 'avatar-xl'
        ];
        
        $sizeClass = $sizeClasses[$size] ?? $sizeClasses['md'];
        
        return sprintf(
            '<div class="avatar %s %s" style="background-color: %s; color: white;">
                <span class="avatar-initials">%s</span>
            </div>',
            $sizeClass,
            $additionalClasses,
            $avatar['color'],
            $avatar['initials']
        );
    }

    /**
     * Met à jour l'avatar d'un utilisateur
     */
    public static function updateUserAvatar($user)
    {
        $avatar = self::generateAvatar($user->name);
        
        $user->update([
            'avatar' => $avatar['initials'],
            'avatar_color' => $avatar['color']
        ]);
        
        return $avatar;
    }

    /**
     * Génère les avatars pour tous les utilisateurs qui n'en ont pas
     */
    public static function generateMissingAvatars()
    {
        $users = \App\Models\User::whereNull('avatar')->orWhereNull('avatar_color')->get();
        
        foreach ($users as $user) {
            self::updateUserAvatar($user);
        }
        
        return $users->count();
    }
}
